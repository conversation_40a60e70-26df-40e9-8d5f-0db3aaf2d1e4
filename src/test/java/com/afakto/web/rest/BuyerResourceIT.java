package com.afakto.web.rest;

import static com.afakto.security.AuthoritiesConstants.WRITER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.cache.CacheManager;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import com.afakto.IntegrationTest;
import com.afakto.domain.Buyer;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.UserRepository;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.mapper.BuyerMapper;

import jakarta.persistence.EntityManager;

/**
 * Integration tests for the {@link BuyerResource} REST controller.
 */
@AutoConfigureMockMvc
@ExtendWith(MockitoExtension.class)
@IntegrationTest
@WithMockUser(authorities = { WRITER })
class BuyerResourceIT {

    private static final String DEFAULT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CODE = "BBBBBBBBBB";

    private static final NumberType DEFAULT_TYPE = NumberType.SIRET;
    private static final NumberType UPDATED_TYPE = NumberType.VAT;

    private static final String DEFAULT_NUMBER = "AAAAAAAAAA";
    private static final String UPDATED_NUMBER = "BBBBBBBBBB";

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/buyers";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    @Autowired
    private BuyerRepository buyerRepository;

    @Autowired
    private BuyerMapper buyerMapper;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restBuyerMockMvc;

    private Buyer buyer;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Buyer createEntity(EntityManager em) {
        var company = CompanyResourceIT.createEntity(em);
        em.persist(company);

        return new Buyer()
                .setCompany(company)
                .setCode(DEFAULT_CODE)
                .setNumberType(DEFAULT_TYPE)
                .setNumber(DEFAULT_NUMBER)
                .setName(DEFAULT_NAME);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Buyer createUpdatedEntity(EntityManager em) {
        var company = CompanyResourceIT.createEntity(em);
        em.persist(company);

        return new Buyer()
                .setCompany(company)
                .setCode(UPDATED_CODE)
                .setNumberType(UPDATED_TYPE)
                .setNumber(UPDATED_NUMBER)
                .setName(UPDATED_NAME);
    }

    @BeforeEach
    public void initTest() {
        buyer = createEntity(em);
        cacheManager.getCache(UserRepository.USERS_BY_LOGIN_CACHE).clear();
    }

    @Test
    @Transactional
    void createBuyer() throws Exception {
        int databaseSizeBeforeCreate = buyerRepository.findAll().size();
        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);
        restBuyerMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isCreated());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeCreate + 1);
        Buyer testBuyer = buyerList.get(buyerList.size() - 1);
        assertThat(testBuyer.getCode()).isEqualTo(DEFAULT_CODE);
        assertThat(testBuyer.getNumberType()).isEqualTo(DEFAULT_TYPE);
        assertThat(testBuyer.getNumber()).isEqualTo(DEFAULT_NUMBER);
        assertThat(testBuyer.getName()).isEqualTo(DEFAULT_NAME);
    }

    @Test
    @Transactional
    void createBuyerWithExistingId() throws Exception {
        // Create the Buyer with an existing ID
        buyer.setId(UUID.randomUUID());
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        int databaseSizeBeforeCreate = buyerRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        restBuyerMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkCodeIsRequired() throws Exception {
        int databaseSizeBeforeTest = buyerRepository.findAll().size();
        // set the field null
        buyer.setCode(null);

        // Create the Buyer, which fails.
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        restBuyerMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTypeIsRequired() throws Exception {
        int databaseSizeBeforeTest = buyerRepository.findAll().size();
        // set the field null
        buyer.setNumberType(null);

        // Create the Buyer, which fails.
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        restBuyerMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNumberIsRequired() throws Exception {
        int databaseSizeBeforeTest = buyerRepository.findAll().size();
        // set the field null
        buyer.setNumber(null);

        // Create the Buyer, which fails.
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        restBuyerMockMvc
                .perform(
                        post(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllBuyers() throws Exception {
        // Initialize the database
        buyerRepository.saveAndFlush(buyer);

        // Get all the buyerList
        restBuyerMockMvc
                .perform(get(ENTITY_API_URL + "?sort=id,desc"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(jsonPath("$.[*].id").value(hasItem(buyer.getId().toString())))
                .andExpect(jsonPath("$.[*].code").value(hasItem(DEFAULT_CODE)))
                .andExpect(jsonPath("$.[*].numberType").value(hasItem(DEFAULT_TYPE.toString())))
                .andExpect(jsonPath("$.[*].number").value(hasItem(DEFAULT_NUMBER)))
                .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)));
    }

    @Test
    @Transactional
    void getBuyer() throws Exception {
        // Initialize the database
        buyerRepository.saveAndFlush(buyer);

        // Get the buyer
        restBuyerMockMvc
                .perform(get(ENTITY_API_URL_ID, buyer.getId()))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(jsonPath("$.id").value(buyer.getId().toString()))
                .andExpect(jsonPath("$.code").value(DEFAULT_CODE))
                .andExpect(jsonPath("$.numberType").value(DEFAULT_TYPE.toString()))
                .andExpect(jsonPath("$.number").value(DEFAULT_NUMBER))
                .andExpect(jsonPath("$.name").value(DEFAULT_NAME));
    }

    @Test
    @Transactional
    void getNonExistingBuyer() throws Exception {
        // Get the buyer
        restBuyerMockMvc.perform(get(ENTITY_API_URL_ID, UUID.randomUUID())).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putNewBuyer() throws Exception {
        // Initialize the database
        buyerRepository.saveAndFlush(buyer);

        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();

        // Update the buyer
        Buyer updatedBuyer = buyerRepository.findById(buyer.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedBuyer are not directly
        // saved in db
        em.detach(updatedBuyer);
        updatedBuyer.setCode(UPDATED_CODE)
                .setNumberType(UPDATED_TYPE)
                .setNumber(UPDATED_NUMBER)
                .setName(UPDATED_NAME);
        BuyerDTO buyerDTO = buyerMapper.toDto(updatedBuyer);

        restBuyerMockMvc
                .perform(
                        put(ENTITY_API_URL_ID, buyerDTO.getId())
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isOk());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
        Buyer testBuyer = buyerList.get(buyerList.size() - 1);
        assertThat(testBuyer.getCode()).isEqualTo(UPDATED_CODE);
        assertThat(testBuyer.getNumberType()).isEqualTo(UPDATED_TYPE);
        assertThat(testBuyer.getNumber()).isEqualTo(UPDATED_NUMBER);
        assertThat(testBuyer.getName()).isEqualTo(UPDATED_NAME);
        // Validate the Buyer in Elasticsearch
        // verify(mockBuyerSearchRepository).save(testBuyer);
    }

    @Test
    @Transactional
    void putNonExistingBuyer() throws Exception {
        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();
        buyer.setId(UUID.randomUUID());

        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restBuyerMockMvc
                .perform(
                        put(ENTITY_API_URL_ID, buyerDTO.getId())
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchBuyer() throws Exception {
        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();
        buyer.setId(UUID.randomUUID());

        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restBuyerMockMvc
                .perform(
                        put(ENTITY_API_URL_ID, UUID.randomUUID())
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamBuyer() throws Exception {
        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();
        buyer.setId(UUID.randomUUID());

        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restBuyerMockMvc
                .perform(
                        put(ENTITY_API_URL)
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isMethodNotAllowed());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateBuyerWithPatch() throws Exception {
        // Initialize the database
        buyerRepository.saveAndFlush(buyer);

        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();

        // Update the buyer using partial update
        Buyer partialUpdatedBuyer = new Buyer();
        partialUpdatedBuyer.setId(buyer.getId());

        partialUpdatedBuyer.setCode(UPDATED_CODE);

        restBuyerMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, partialUpdatedBuyer.getId())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(partialUpdatedBuyer)))
                .andExpect(status().isOk());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
        Buyer testBuyer = buyerList.get(buyerList.size() - 1);
        assertThat(testBuyer.getCode()).isEqualTo(UPDATED_CODE);
        assertThat(testBuyer.getNumberType()).isEqualTo(DEFAULT_TYPE);
        assertThat(testBuyer.getNumber()).isEqualTo(DEFAULT_NUMBER);
        assertThat(testBuyer.getName()).isEqualTo(DEFAULT_NAME);
    }

    @Test
    @Transactional
    void fullUpdateBuyerWithPatch() throws Exception {
        // Initialize the database
        buyerRepository.saveAndFlush(buyer);

        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();

        // Update the buyer using partial update
        Buyer partialUpdatedBuyer = new Buyer()
                .setCode(UPDATED_CODE)
                .setNumberType(UPDATED_TYPE)
                .setNumber(UPDATED_NUMBER)
                .setName(UPDATED_NAME);
        partialUpdatedBuyer.setId(buyer.getId());

        restBuyerMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, partialUpdatedBuyer.getId())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(partialUpdatedBuyer)))
                .andExpect(status().isOk());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
        Buyer testBuyer = buyerList.get(buyerList.size() - 1);
        assertThat(testBuyer.getCode()).isEqualTo(UPDATED_CODE);
        assertThat(testBuyer.getNumberType()).isEqualTo(UPDATED_TYPE);
        assertThat(testBuyer.getNumber()).isEqualTo(UPDATED_NUMBER);
        assertThat(testBuyer.getName()).isEqualTo(UPDATED_NAME);
    }

    @Test
    @Transactional
    void patchNonExistingBuyer() throws Exception {
        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();
        buyer.setId(UUID.randomUUID());

        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restBuyerMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, buyerDTO.getId())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchBuyer() throws Exception {
        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();
        buyer.setId(UUID.randomUUID());

        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restBuyerMockMvc
                .perform(
                        patch(ENTITY_API_URL_ID, UUID.randomUUID())
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isBadRequest());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamBuyer() throws Exception {
        int databaseSizeBeforeUpdate = buyerRepository.findAll().size();
        buyer.setId(UUID.randomUUID());

        // Create the Buyer
        BuyerDTO buyerDTO = buyerMapper.toDto(buyer);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restBuyerMockMvc
                .perform(
                        patch(ENTITY_API_URL)
                                .with(csrf())
                                .contentType("application/merge-patch+json")
                                .content(TestUtil.convertObjectToJsonBytes(buyerDTO)))
                .andExpect(status().isMethodNotAllowed());

        // Validate the Buyer in the database
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteBuyer() throws Exception {
        // Initialize the database
        buyerRepository.saveAndFlush(buyer);

        int databaseSizeBeforeDelete = buyerRepository.findAll().size();

        // Delete the buyer
        restBuyerMockMvc
                .perform(delete(ENTITY_API_URL_ID, buyer.getId()).with(csrf()).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<Buyer> buyerList = buyerRepository.findAll();
        assertThat(buyerList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
