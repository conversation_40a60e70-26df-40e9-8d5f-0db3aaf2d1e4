package com.afakto.batch.bnp;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

import java.math.BigDecimal;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import com.afakto.domain.Address;
import com.afakto.domain.Buyer;
import com.afakto.domain.Cession;
import com.afakto.domain.Contact;
import com.afakto.domain.Contract;
import com.afakto.domain.CreditLimit;
import com.afakto.domain.contractdata.FinancialInformation;
import com.afakto.domain.enumeration.NumberType;

@ExtendWith(MockitoExtension.class)
public class BnpDebtorExportJobTest {

    @InjectMocks
    private BnpDebtorExportJob bnpDebtorExportJob;

    @Test
    public void testExtract() {
        Buyer buyer = new Buyer()
                .setCode("12345")
                .setName("Test Buyer")
                .setNumberType(NumberType.SIREN)
                .setNumber("123456789")
                .setAddress(new Address()
                        .setStreetNumber("123")
                        .setStreetName("Main St")
                        .setPostalCode("12345")
                        .setCity("Test City")
                        .setCountry("FR"))
                .setContact(new Contact()
                        .setName("John Doe")
                        .setPhone("1234567890"))
                .setCreditLimit(new CreditLimit()
                        .setAmount(new BigDecimal("123456.78")));

        var cession = new Cession()
                .setContract(new Contract()
                        .setContractNumber("001")
                        .setFinancialInformation(new FinancialInformation().setCurrency("EUR")));

        // Extract fields
        var extractedFields = bnpDebtorExportJob.createFieldExtractor(cession).extract(buyer);

        String[] expectedFields = {
                "12345", // Code client
                "Test Buyer", // Raison sociale
                "123 Main St", // Adresse 1
                "", // Adresse 2
                "12345", // Code postal
                "Test City", // Ville
                "John Doe", // Interlocuteur
                "1234567890", // Téléphone
                "", // Fax
                "123456789", // Identifiant international
                "Siren", // Type d’identifiant
                "", // NIC
                "124000", // Montant de l’agrément
                "001", // Numéro du contrat
                "EUR", // Devise
                "", // Zone réservée
                "C", // Code contrat
                "", // Mode de paiement
                "", // RIB
                "FR" // Code pays
        };

        assertArrayEquals(expectedFields, extractedFields);
    }

    @Test
    public void testExtractMoreEmpty() {
        Buyer buyer = new Buyer()
                .setCode("12345")
                .setName("Test Buyer")
                .setNumberType(NumberType.SIREN)
                .setNumber("123456789")
                .setAddress(new Address()
                        .setCountry("FR"))
                .setCreditLimit(new CreditLimit()
                        .setAmount(new BigDecimal("123456.78")));

        var cession = new Cession()
                .setContract(new Contract()
                        .setContractNumber("001")
                        .setFinancialInformation(new FinancialInformation().setCurrency("EUR")));

        // Extract fields
        var extractedFields = bnpDebtorExportJob.createFieldExtractor(cession).extract(buyer);

        String[] expectedFields = {
                "12345", // Code client
                "Test Buyer", // Raison sociale
                ".", // Adresse 1
                "", // Adresse 2
                "", // Code postal
                "", // Ville
                "", // Interlocuteur
                "", // Téléphone
                "", // Fax
                "123456789", // Identifiant international
                "Siren", // Type d’identifiant
                "", // NIC
                "124000", // Montant de l’agrément
                "001", // Numéro du contrat
                "EUR", // Devise
                "", // Zone réservée
                "C", // Code contrat
                "", // Mode de paiement
                "", // RIB
                "FR" // Code pays
        };

        assertArrayEquals(expectedFields, extractedFields);
    }

}
