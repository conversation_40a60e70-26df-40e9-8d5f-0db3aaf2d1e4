# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

---
# Conditionally disable springdoc on missing api-docs profile
spring:
  config:
    activate:
      on-profile: '!api-docs'
springdoc:
  api-docs:
    enabled: false
---
management:
  endpoints:
    web:
      base-path: /management
      exposure:
        include:
          - configprops
          - env
          - health
          - info
          - jhimetrics
          - jhiopenapigroups
          - logfile
          - loggers
          - prometheus
          - threaddump
          - caches
          - liquibase
  endpoint:
    health:
      show-details: when_authorized
      roles: 'ROLE_ADMIN'
      probes:
        enabled: true
      group:
        liveness:
          include: livenessState
        readiness:
          include: readinessState,db
    jhimetrics:
      enabled: true
  info:
    git:
      mode: full
    env:
      enabled: true
  health:
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
  prometheus:
    metrics:
      export:
        enabled: true
        step: 60
  observations:
    key-values:
      application: ${spring.application.name}
  metrics:
    enable:
      http: true
      jvm: false
      logback: true
      process: false
      system: false
    distribution:
      percentiles-histogram:
        all: false
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    data:
      repository:
        autotime:
          enabled: false

# To enable/disable azure customMetric
instrumentation:
  micrometer:
    enabled: false

spring:
  application:
    name: Afakto
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: #@spring.profiles.active@#
    group:
      dev:
        - dev
        - api-docs
        # Uncomment to activate TLS for the dev profile
        #- tls
      prod:
        - prod
        - api-docs
  jmx:
    enabled: false
  batch:
    job:
      enabled: true
  data:
    jpa:
      repositories:
        bootstrap-mode: deferred
    web.pageable.max-page-size: 10000
  jpa:
    open-in-view: false
    properties:
      hibernate:
        jdbc.time_zone: UTC
        timezone.default_storage: NORMALIZE
        type.preferred_instant_jdbc_type: TIMESTAMP
        id.new_generator_mappings: true
        connection.provider_disables_autocommit: true
        cache.use_second_level_cache: true
        cache.use_query_cache: true
        javax.cache.missing_cache_strategy: create
        generate_statistics: false
        # modify batch size as necessary
        jdbc.batch_size: 25
        jdbc.fetch_size: 25
        default_batch_fetch_size: 25
        order_inserts: true
        order_updates: true
        query.fail_on_pagination_over_collection_fetch: true
        query.in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
  mvc:
    problemdetails:
      enabled: true
  security:
    oauth2:
      client:
        provider:
          oidc:
            issuer-uri: ${OAUTH_URI}
        registration:
          oidc:
            client-id: ${OAUTH_CLIENT_ID}
            client-secret: ${OAUTH_CLIENT_SECRET}
            scope: ${OAUTH_SCOPE:openid, profile, email, offline_access}
      resourceserver:
        jwt:
          # Useful for JWT decoding
          jwk-set-uri: ${OAUTH_URI}
  servlet.multipart.max-file-size: 20MB
  task:
    execution:
      thread-name-prefix: afakto-task-
      pool:
        core-size: 2
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: afakto-scheduling-
      pool:
        size: 2
  thymeleaf:
    mode: HTML
  output:
    ansi:
      console-available: true

server:
  servlet:
    session:
      cookie:
        http-only: true
      timeout: 30m

springdoc:
  show-actuator: false  # Disable showing actuator/management endpoints in Swagger
  swagger-ui:
    path: /swagger-ui
    operationsSorter: alpha
    tagsSorter: alpha
    docExpansion: none
    filter: true
    disable-swagger-default-url: true
  api-docs:
    path: /v3/api-docs
    enabled: true
  packages-to-scan: com.afakto

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'afaktoApp'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #   allowed-origins: "http://localhost:8100,http://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params"
  #   allow-credentials: true
  #   max-age: 1800
  mail:
    from: Afakto@localhost
  api-docs:
    # Removed management-include-pattern to exclude management endpoints from API docs
    title: Afakto API
    description: A factoring web application
    version: 1.0.0
    contact-name: Simon Daller
    contact-email: <EMAIL>
  security:
    content-security-policy: >-
      default-src 'self';
      frame-src 'self' data:;
      script-src 'self' 'unsafe-inline' 'unsafe-eval';
      style-src 'self' 'unsafe-inline';
      img-src * 'self' data: https:;
      font-src 'self' data:
    oauth2:
      audience:
        - ${OAUTH_URI_MGMT}api/v2/

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

application:
  # Where input/output files are stored, and other files
  filesystem: /var/afakto
  # Siren Connection
  insee:
    base-url: https://api.insee.fr/entreprises/sirene/V3.11/
    auth:
      consumer-key: ${INSEE_KEY}
      consumer-secret: ${INSEE_SECRET}
      tokenUrl: https://api.insee.fr/token
  auth0-management:
    issuer-uri: ${OAUTH_URI_MGMT}
    client-id: ${OAUTH_CLIENT_ID_MGMT}
    client-secret: ${OAUTH_CLIENT_SECRET_MGMT:}
  insurer:
    atradius:
      base-url: https://api-uat.atradius.com
    coface:
      base-url: https://api.coface.com
