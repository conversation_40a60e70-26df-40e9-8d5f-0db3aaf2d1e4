<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.contract.home.title') }}</h1>

      <q-space />

      <q-btn
        v-if="hasRoleConfig && entity.id && entity.creditInsurance === 'EXTERNAL' && entity.creditInsurancePolicy.id"
        icon="sync"
        :label="$t('afaktoApp.contract.creditInsurance.updateCreditLimits')"
        @click="updateCreditLimits"
      >
        <q-tooltip>{{ $t('afaktoApp.contract.creditInsurance.updateCreditLimitsHint') }}</q-tooltip>
      </q-btn>
      <q-btn v-if="hasRoleConfig && entity.id" class="buttonNeutral" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn class="buttonNeutral" icon="close" to="/contracts">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-tabs v-model="tab" :vertical="$q.platform.is?.desktop">
        <q-tab name="main" :label="t('afaktoApp.contract.detail.title')" />
        <q-tab name="secondary" :label="t('afaktoApp.contract.detail.secondary')" />
        <q-tab name="insurer" :label="t('afaktoApp.contract.detail.insurer')" />
        <q-tab name="factor" :label="t('afaktoApp.contract.detail.factor')" />
      </q-tabs>

      <q-tab-panels v-model="tab" animated swipeable :vertical="$q.platform.is?.desktop">
        <q-tab-panel name="main">
          <q-select
            v-model="entity.company"
            class="required"
            filled
            :label="$t('afaktoApp.contract.company.name')"
            :options="useAuthenticationStore().account.companies"
            option-label="name"
            option-value="id"
            :readonly="!hasRoleConfig || !!entity.id"
            :rules="[required]"
          />

          <q-select
            v-model="entity.financialInformation.currency"
            class="required"
            filled
            :label="$t('afaktoApp.contract.financialInformation.currency')"
            :options="allCurrencies"
            :option-label="item => item"
            :option-value="item => item"
            :readonly="!hasRoleConfig"
            :rules="[required]"
          />

          <q-input
            v-model="entity.signatureDate"
            filled
            :label="$t('afaktoApp.contract.signatureDate')"
            :readonly="!hasRoleConfig"
            type="date"
          />
          <q-input
            v-model="entity.activationDate"
            filled
            :label="$t('afaktoApp.contract.activationDate')"
            :readonly="!hasRoleConfig"
            type="date"
          >
            <template #append>
              <q-icon name="info">
                <q-tooltip>{{ $t('afaktoApp.contract.activationDate_help') }}</q-tooltip>
              </q-icon>
            </template>
          </q-input>
        </q-tab-panel>

        <q-tab-panel name="secondary">
          <q-toggle v-model="entity.syndicatedProgram" :disable="!hasRoleConfig" :label="$t('afaktoApp.contract.syndicatedProgram')" />
          <q-toggle v-model="entity.confidentialProgram" :disable="!hasRoleConfig" :label="$t('afaktoApp.contract.confidentialProgram')" />
          <q-toggle v-model="entity.withRecourse" :disable="!hasRoleConfig" :label="$t('afaktoApp.contract.withRecourse')" />

          <q-select
            v-model="entity.programType"
            filled
            :label="$t('afaktoApp.contract.programType')"
            :option-label="item => (item === null ? '' : $t(`afaktoApp.contract.programType_${item}`))"
            :options="['LINE_BY_LINE', 'BALANCE_BY_RELOADING', 'BALANCE_BY_CANCELLING_AND_REPLACING']"
            :readonly="!hasRoleConfig"
          />

          <b-input
            v-model="entity.monthlyMaxCession"
            :label="$t('afaktoApp.contract.monthlyMaxCession')"
            :readonly="!hasRoleConfig"
            type="number"
          />

          <b-input
            v-model="entity.defaultOverDue"
            :label="$t('afaktoApp.contract.defaultOverDue')"
            :readonly="!hasRoleConfig"
            :suffix="$t('entity.detail.days').toLowerCase()"
            type="number"
          >
            <template #append>
              <q-icon name="info">
                <q-tooltip size="lg">{{ $t('afaktoApp.contract.defaultOverDue_help') }}</q-tooltip>
              </q-icon>
            </template>
          </b-input>

          <q-select
            v-model="entity.country"
            emit-value
            filled
            :label="$t('afaktoApp.contract.country')"
            map-options
            :options="countries.filter(c => c.iso)"
            option-label="name"
            option-value="code"
            :readonly="!hasRoleConfig"
          >
            <template v-if="entity.country" #prepend>
              <span :class="`fi fi-${entity.country}`"></span>
            </template>
            <template #option="scope">
              <q-item v-bind="scope.itemProps" dense>
                <q-item-section side>
                  <span :class="`fi fi-${scope.opt.code}`" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>

          <q-select
            v-model="entity.countries"
            emit-value
            filled
            :label="$t('afaktoApp.contract.countries')"
            map-options
            multiple
            :options="countries.filter(c => c.iso)"
            option-label="name"
            option-value="code"
            :readonly="!hasRoleConfig"
          >
            <template #option="scope">
              <q-item v-bind="scope.itemProps" dense>
                <q-item-section side>
                  <span :class="`fi fi-${scope.opt.code}`" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.name }}</q-item-label>
                </q-item-section>
              </q-item> </template
            >"
          </q-select>
        </q-tab-panel>

        <q-tab-panel name="insurer">
          <q-select
            v-model="entity.creditInsurance"
            filled
            :label="$t('afaktoApp.contract.creditInsurance.type')"
            :options="['INTERNAL', 'EXTERNAL']"
            :option-label="item => (item === null ? '' : $t(`afaktoApp.contract.creditInsurance.type_${item}`))"
            :readonly="!hasRoleConfig"
            @update:model-value="value => (entity.creditInsurancePolicy = value == 'EXTERNAL' ? {} : null)"
          />

          <template v-if="entity.creditInsurance === 'EXTERNAL'">
            <h3>{{ $t('afaktoApp.externalCreditInsurance.policy.title') }}</h3>

            <b-selector
              v-model="entity.creditInsurancePolicy.externalCreditInsurance"
              class="required"
              :label="$t('afaktoApp.externalCreditInsurance.name')"
              option-label="name"
              option-value="id"
              :readonly="!hasRoleConfig"
              :rules="[required]"
              url="/api/external-credit-insurances?sort=name"
            />
            <b-input
              v-if="
                entity.creditInsurancePolicy.externalCreditInsurance &&
                entity.creditInsurancePolicy.externalCreditInsurance?.name !== 'Coface'
              "
              v-model="entity.creditInsurancePolicy.customerId"
              class="required"
              :label="$t('afaktoApp.externalCreditInsurance.policy.customerId')"
              :readonly="!hasRoleConfig"
              :rules="[required]"
            />
            <b-input
              v-if="entity.creditInsurancePolicy.externalCreditInsurance"
              v-model="entity.creditInsurancePolicy.policyId"
              class="required"
              :label="$t('afaktoApp.externalCreditInsurance.policy.policyId')"
              :readonly="!hasRoleConfig"
              :rules="[required]"
            />
            <q-toggle
              v-model="nonSpecifiedCoverage"
              :label="
                $t('afaktoApp.externalCreditInsurance.policy.blindCover') +
                ' - ' +
                $t('afaktoApp.externalCreditInsurance.policy.blindCover_help')
              "
              :disable="!hasRoleConfig"
            />
            <b-input
              v-if="nonSpecifiedCoverage"
              v-model="entity.creditInsurancePolicy.blindCoverAmount"
              :label="$t('afaktoApp.externalCreditInsurance.policy.blindCoverAmount')"
              :readonly="!hasRoleConfig"
              type="currency"
            />
          </template>
        </q-tab-panel>

        <q-tab-panel name="factor">
          <b-selector
            v-model="entity.factorInstitution"
            class="required"
            :label="$t('afaktoApp.contract.factorInstitution.name')"
            option-label="name"
            option-value="id"
            :readonly="!hasRoleConfig || !!entity.id"
            :rules="[required]"
            url="/api/factor-institutions?sort=name"
          />

          <b-input v-model="entity.contractNumber" :label="$t('afaktoApp.contract.contractNumber')" :readonly="!hasRoleConfig" />

          <b-input v-model="entity.factorAccountNumber" :label="$t('afaktoApp.contract.factorAccountNumber')" :readonly="!hasRoleConfig" />

          <q-toggle v-model="entity.cashIn" :disable="!hasRoleConfig" :label="$t('afaktoApp.contract.cashIn')">
            <template #default>
              <q-icon class="on-right" name="info" size="xs">
                <q-tooltip size="lg">{{ $t('afaktoApp.contract.cashIn_help') }}</q-tooltip>
              </q-icon>
            </template>
          </q-toggle>
          <template v-if="entity.cashIn">
            <b-input
              v-model="entity.bic"
              class="required"
              :label="$t('afaktoApp.contract.bic')"
              :readonly="!hasRoleConfig"
              :rules="[required]"
            />
            <b-input
              v-model="entity.iban"
              class="required"
              :label="$t('afaktoApp.contract.iban')"
              :readonly="!hasRoleConfig"
              :rules="[required]"
            />
            <b-input
              v-model="entity.journal"
              class="required"
              :label="$t('afaktoApp.contract.journal')"
              :readonly="!hasRoleConfig"
              :rules="[required]"
            />
          </template>

          <h3>{{ $t('afaktoApp.contract.financialInformation.authorisation') }}</h3>

          <b-input
            v-model="entity.financialInformation.guaranteeLine"
            :label="$t('afaktoApp.contract.financialInformation.guaranteeLine')"
            :readonly="!hasRoleConfig"
            type="currency"
          />
          <b-input
            v-model="entity.financialInformation.nonGuaranteeLine"
            :label="$t('afaktoApp.contract.financialInformation.nonGuaranteeLine')"
            :readonly="!hasRoleConfig"
            type="currency"
          />
          <b-input
            v-model="entity.financialInformation.guaranteeFund"
            :label="$t('afaktoApp.contract.financialInformation.guaranteeFund')"
            :readonly="!hasRoleConfig"
            type="percent"
          />

          <h3>{{ $t('afaktoApp.contract.financialInformation.cost') }}</h3>

          <b-input
            v-model="entity.financialInformation.factoringCommission"
            :label="$t('afaktoApp.contract.financialInformation.factoringCommission')"
            :readonly="!hasRoleConfig"
            type="percent"
          />
          <b-input
            v-model="entity.financialInformation.minFactoringCommission"
            :label="$t('afaktoApp.contract.financialInformation.minFactoringCommission')"
            :readonly="!hasRoleConfig"
            type="currency"
          />
          <b-input
            v-model="entity.financialInformation.margin"
            :label="$t('afaktoApp.contract.financialInformation.margin')"
            :readonly="!hasRoleConfig"
            type="percent"
          />
          <b-input
            v-model="entity.financialInformation.indexRate"
            :label="$t('afaktoApp.contract.financialInformation.indexRate')"
            :readonly="!hasRoleConfig"
            type="percent"
          />
          <b-input
            v-model="entity.financialInformation.indexRateFloor"
            :label="$t('afaktoApp.contract.financialInformation.indexRateFloor')"
            :readonly="!hasRoleConfig"
            type="percent"
          />
        </q-tab-panel>
      </q-tab-panels>

      <div align="center" class="full-width">
        <q-btn v-if="hasRoleConfig" class="buttonBrand" icon="check" :label="$t('entity.action.save')" size="md" type="submit" />
      </div>
    </q-form>

    <entity-meta :entity="entity" />

    <q-inner-loading :showing="updating">
      <q-spinner-gears color="primary" size="10em" />
    </q-inner-loading>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import countries from 'flag-icons/country.json';
import { required } from 'src/components/rules';
import EntityMeta from 'src/pages/subcomponents/EntityMeta.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';
import ContractService from './contract.service';

const baseApiUrl = '/api/contracts';
const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const { notifyError } = useNotifications();
const nonSpecifiedCoverage = ref(false);
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const tab = ref(route.query.tab || 'main');
const updating = ref(false);

const allCurrencies = ref([]);
const entity = ref({
  countries: [],
  creditInsurance: 'INTERNAL',
  creditInsurancePolicy: null,
  financialInformation: {
    currency: null,
  },
});

onMounted(async () => {
  if (route.params.id) {
    try {
      entity.value = await ContractService.get(route.params.id);
      nonSpecifiedCoverage.value = entity.value.creditInsurancePolicy?.blindCoverAmount != null;
    } catch (error) {
      notifyError(error);
    }
  }

  allCurrencies.value = (await api.get('/api/contracts/allCurrencies')).data;

  setupBeforeUnload(t, document.forms[0], entity.value);
});

const onSubmit = async () => {
  try {
    const response = await ContractService.save(entity.value);
    if (entity.value.creditInsurance === 'EXTERNAL' && !entity.value.creditInsurancePolicy.id) {
      entity.value = response.data;
      await updateCreditLimits();
    }
    router.back();
  } catch (err) {
    notifyError(err);
  }
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.contract.delete.question', { id: entity.value.contractNumber }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    ContractService.delete(entity.value.id)
      .then(() => {
        $q.notify({
          message: 'Deleted',
          icon: 'announcement',
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};

const updateCreditLimits = async () => {
  updating.value = true;
  try {
    const response = await api.post(`${baseApiUrl}/${entity.value.id}/updateCreditLimits`);
    $q.notify({
      color: 'positive',
      icon: 'sync',
      message: t('afaktoApp.contract.creditInsurance.updatedCreditLimits', { nb: response.data }),
    });
  } catch (error) {
    notifyError(error);
  }
  updating.value = false;
};

watch(nonSpecifiedCoverage, value => {
  if (!value) {
    entity.value.creditInsurancePolicy.blindCoverAmount = null;
  }
});
</script>
