<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.bankTransaction.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <q-btn
          v-if="hasRoleConfig"
          class="buttonNeutral"
          icon="format_list_numbered_rtl"
          :label="$t('entity.action.configure')"
          to="/category-to-accounts/new"
        />

        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn
          class="buttonBrand"
          icon="list"
          :label="$t('afaktoApp.bankTransaction.with-offset.title')"
          to="bank-transactions-with-offset"
        />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import useNotifications from 'src/util/useNotifications';
import { BANK_TRANSACTION_TYPE } from '/src/constants/bankTransaction';
import { filtersToQueryParams } from '/src/util/filtering';
import { format } from '/src/util/format';

const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const baseApiUrl = '/api/bank-transactions';
const { n, t } = useI18n();
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.company.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name })),
    },
    label: t('afaktoApp.bankTransaction.company.name'),
    name: 'company.name',
    sortable: true,
  },
  {
    field: 'date',
    filter: { type: 'date' },
    format: value => format(value),
    label: t('afaktoApp.bankTransaction.date'),
    name: 'date',
    sortable: true,
  },
  {
    field: 'valueDate',
    filter: { type: 'date' },
    format: value => format(value),
    label: t('afaktoApp.bankTransaction.valueDate'),
    name: 'valueDate',
    sortable: true,
  },
  {
    align: 'left',
    field: 'type',
    filter: {
      type: 'enum',
      values: [
        { label: t('afaktoApp.bankTransaction.type_CREDIT'), value: 'CREDIT' },
        { label: t('afaktoApp.bankTransaction.type_DEBIT'), value: 'DEBIT' },
      ],
      translated_labels: [,],
    },
    format: value => t(`afaktoApp.bankTransaction.type_${value}`),
    label: t('afaktoApp.bankTransaction.type'),
    name: 'type',
    sortable: true,
  },
  {
    field: 'currency',
    filter: { type: 'currency' },
    label: t('afaktoApp.bankTransaction.currency'),
    name: 'currency',
    sortable: true,
  },
  {
    classes: props => (props.amount < 0 ? 'text-negative' : 'text-positive'),
    field: 'amount',
    filter: { type: 'number' },
    format: (value, row) => n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.bankTransaction.amount'),
    name: 'amount',
    sortable: true,
  },
  {
    classes: props => (props.balance < 0 ? 'text-negative' : 'text-positive'),
    field: 'balance',
    filter: { type: 'number' },
    format: (value, row) => n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.bankTransaction.balance'),
    name: 'balance',
    sortable: true,
  },
  {
    align: 'left',
    classes: 'text-capitalize',
    field: 'category',
    filter: {
      type: 'enum',
      values: BANK_TRANSACTION_TYPE.map(category => ({
        label: category.replace(/_/g, ' ').toLowerCase(),
        value: category,
      })),
    },
    format: value => value?.replace(/_/g, ' ')?.toLowerCase(),
    label: t('afaktoApp.bankTransaction.category'),
    name: 'category',
    sortable: true,
  },
  {
    align: 'left',
    field: 'ledgerAccount',
    filter: { type: 'text' },
    label: t('afaktoApp.bankTransaction.ledgerAccount'),
    name: 'ledgerAccount',
    sortable: true,
  },
  {
    align: 'left',
    field: 'narrative',
    filter: { type: 'text' },
    label: t('afaktoApp.bankTransaction.narrative'),
    name: 'narrative',
    sortable: true,
  },
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        ...filtersToQueryParams(filters.value),
      },
    });
    rows.value = response.data;

    pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
    router.replace({ query: { page, sortBy, descending, rowsPerPage } });
  } catch (err) {
    notifyError(err);
  }
};
</script>
