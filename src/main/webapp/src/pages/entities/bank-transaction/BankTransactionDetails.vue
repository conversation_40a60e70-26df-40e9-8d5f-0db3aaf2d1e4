<template>
  <q-page class="flex justify-center">
    <b-title :model-value="$t('afaktoApp.bankTransaction.detail.title')" />

    <q-page-sticky position="top-right" :offset="[18, 18]">
      <q-btn v-if="bankTransaction.data.id" color="secondary" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn color="primary" icon="close" to="/bank-transactions">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-page-sticky>

    <q-form @submit="onSubmit">
      <q-card>
        <q-card-section>
          <b-selector
            v-model="bankTransaction.data.company"
            :label="$t('afaktoApp.bankTransaction.company.name')"
            option-value="id"
            option-label="name"
            url="/api/companies?sort=name"
          />
          <b-selector
            v-model="bankTransaction.data.factorInstitution"
            :label="$t('afaktoApp.bankTransaction.factorInstitution')"
            option-value="id"
            option-label="name"
            url="/api/factor-institutions?sort=name"
          />

          <b-input
            v-model="v$.data.bankTransactionNumber.$model"
            :label="$t('afaktoApp.bankTransaction.bankTransactionNumber')"
            :error="v$.data.bankTransactionNumber.$error"
            :error-messages="v$.data.bankTransactionNumber.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.factorAccountNumber"
            :label="$t('afaktoApp.bankTransaction.factorAccountNumber')"
            @keydown.enter.prevent
          />

          <q-toggle v-model="bankTransaction.data.syndicatedProgram" :label="$t('afaktoApp.bankTransaction.syndicatedProgram')"></q-toggle>
          <q-toggle
            v-model="bankTransaction.data.confidentialProgram"
            :label="$t('afaktoApp.bankTransaction.confidentialProgram')"
          ></q-toggle>
          <q-toggle v-model="bankTransaction.data.withRecourse" :label="$t('afaktoApp.bankTransaction.withRecourse')"></q-toggle>
          <q-toggle v-model="bankTransaction.data.unfunding" :label="$t('afaktoApp.bankTransaction.unfunding')"></q-toggle>
          <b-input
            v-model="bankTransaction.data.monthlyMaxCession"
            type="number"
            :label="$t('afaktoApp.bankTransaction.monthlyMaxCession')"
            :error="v$.data.monthlyMaxCession.$error"
            :error-messages="v$.data.monthlyMaxCession.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.signatureDate"
            type="date"
            :label="$t('afaktoApp.bankTransaction.signatureDate')"
            :error="v$.data.signatureDate.$error"
            :error-messages="v$.data.signatureDate.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.activationDate"
            type="date"
            :label="$t('afaktoApp.bankTransaction.activationDate')"
            :error="v$.data.activationDate.$error"
            :error-messages="v$.data.activationDate.$errors"
            @keydown.enter.prevent
          />
          <q-select
            v-model="bankTransaction.data.programType"
            filled
            :label="$t('afaktoApp.bankTransaction.programType')"
            :options="Object.values(FactoringProgramType)"
            :error="v$.data.programType.$error"
            :error-messages="v$.data.programType.$errors"
            hide-bottom-space
          />
        </q-card-section>
      </q-card>

      <q-card>
        <q-card-section>
          <q-item-label caption>Financial Information</q-item-label>
          <b-currency v-model="bankTransaction.data.financialInformation.currency" class="col-3"></b-currency>

          <q-item-label caption> Financing Authorization </q-item-label>
          <b-input
            v-model="bankTransaction.data.financialInformation.financingAuthorization.guaranteeLine"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.financingAuthorization.guaranteeLine')"
            :error="v$.data.financialInformation.financingAuthorization.guaranteeLine.$error"
            :error-messages="v$.data.financialInformation.financingAuthorization.guaranteeLine.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.financialInformation.financingAuthorization.nonGuaranteeLine"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.financingAuthorization.nonGuaranteeLine')"
            :error="v$.data.financialInformation.financingAuthorization.nonGuaranteeLine.$error"
            :error-messages="v$.data.financialInformation.financingAuthorization.nonGuaranteeLine.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.financialInformation.guaranteeFund"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.guaranteeFund')"
            :error="v$.data.financialInformation.guaranteeFund.$error"
            :error-messages="v$.data.financialInformation.guaranteeFund.$errors"
            @keydown.enter.prevent
          />

          <q-item-label caption> Cost </q-item-label>

          <b-input
            v-model="bankTransaction.data.financialInformation.cost.factoringCommission"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.cost.factoringCommission')"
            :error="v$.data.financialInformation.cost.factoringCommission.$error"
            :error-messages="v$.data.financialInformation.cost.factoringCommission.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.financialInformation.cost.minFactoringCommission"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.cost.minFactoringCommission')"
            :error="v$.data.financialInformation.cost.minFactoringCommission.$error"
            :error-messages="v$.data.financialInformation.cost.minFactoringCommission.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.financialInformation.cost.financingCommission.margin"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.cost.financingCommission.margin')"
            :error="v$.data.financialInformation.cost.financingCommission.margin.$error"
            :error-messages="v$.data.financialInformation.cost.financingCommission.margin.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.financialInformation.cost.financingCommission.indexRate"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.cost.financingCommission.indexRate')"
            :error="v$.data.financialInformation.cost.financingCommission.indexRate.$error"
            :error-messages="v$.data.financialInformation.cost.financingCommission.indexRate.$errors"
            @keydown.enter.prevent
          />
          <b-input
            v-model="bankTransaction.data.financialInformation.cost.financingCommission.indexRateFloor"
            type="number"
            :label="$t('afaktoApp.bankTransaction.financialInformation.cost.financingCommission.indexRateFloor')"
            :error="v$.data.financialInformation.cost.financingCommission.indexRateFloor.$error"
            :error-messages="v$.data.financialInformation.cost.financingCommission.indexRateFloor.$errors"
            @keydown.enter.prevent
          />
        </q-card-section>
      </q-card>

      <q-card-actions>
        <q-btn
          class="full-width"
          color="primary"
          icon="label_important"
          :label="$t('entity.action.save')"
          :loading="loading"
          :disable="loading"
          type="submit"
        />
      </q-card-actions>
    </q-form>
  </q-page>
</template>

<script setup>
import useVuelidate from '@vuelidate/core';
import { required } from '@vuelidate/validators';
import { useQuasar } from 'quasar';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { FactoringProgramType } from 'src/pages/entities/contract/contract.service';
import BankTransactionService from './bank-transaction.service';

const { t } = useI18n();
const $q = useQuasar();
const route = useRoute();
const router = useRouter();

const bankTransaction = reactive({
  data: {
    id: null,
    company: null,
    factorInstitution: null,
    bankTransactionNumber: null,
    factorAccountNumber: null,
    syndicatedProgram: false,
    confidentialProgram: false,
    withRecourse: false,
    unfunding: false,
    monthlyMaxCession: null,
    signatureDate: null,
    activationDate: null,
    programType: null,
    financialInformation: {
      currency: null,
      financingAuthorization: {
        guaranteeLine: null,
        nonGuaranteeLine: null,
      },
      guaranteeFund: null,
      cost: {
        factoringCommission: null,
        minFactoringCommission: null,
        financingCommission: {
          margin: null,
          indexRate: null,
          indexRateFloor: null,
        },
      },
    },
  },
});

const rules = {
  data: {
    company: {
      id: { required, $autoDirty: true },
    },
    factorInstitution: {
      id: { required, $autoDirty: true },
    },
    bankTransactionNumber: { required, $autoDirty: true },
    factorAccountNumber: { required, $autoDirty: true },
    syndicatedProgram: { required, $autoDirty: true },
    confidentialProgram: { required, $autoDirty: true },
    withRecourse: { required, $autoDirty: true },
    unfunding: { required, $autoDirty: true },
    monthlyMaxCession: { required, $autoDirty: true },
    signatureDate: { $autoDirty: true },
    activationDate: { $autoDirty: true },
    programType: { required, $autoDirty: true },
    financialInformation: {
      currency: { $autoDirty: true },
      financingAuthorization: {
        guaranteeLine: { $autoDirty: true },
        nonGuaranteeLine: { $autoDirty: true },
      },
      guaranteeFund: { $autoDirty: true },
      cost: {
        factoringCommission: { $autoDirty: true },
        minFactoringCommission: { $autoDirty: true },
        financingCommission: {
          margin: { $autoDirty: true },
          indexRate: { $autoDirty: true },
          indexRateFloor: { $autoDirty: true },
        },
      },
    },
  },
};

const loading = ref(false);

const bankTransactionId = ref(route.params.id);

onMounted(() => {
  if (!bankTransactionId.value) return;

  loading.value = true;
  BankTransactionService.get(bankTransactionId.value)
    .then(data => (bankTransaction.data = data))
    .catch(err => {
      $q.notify({
        type: 'negative',
        message: t(err.response.data.message),
      });
    })
    .finally(() => (loading.value = false));
});

const v$ = useVuelidate(rules, bankTransaction);

const onSubmit = async () => {
  const isFormCorrect = await v$.value.$validate();
  // you can show some extra alert to the user or just leave the each field to show it's `$errors`.
  if (!isFormCorrect) {
    console.log(JSON.stringify(v$.value.$errors));
    return;
  }
  loading.value = true;
  BankTransactionService.save(bankTransaction.data)
    .then(() => {
      router.back();
    })
    .catch(err => {
      loading.value = false;
      $q.notify({
        type: 'negative',
        message: t(err.response.data.message),
      });
    });
};

const onDelete = () => {
  const id = bankTransaction.data.id;
  $q.dialog({
    message: t('afaktoApp.bankTransaction.delete.question', { id: id }),
    cancel: true,
    persistent: true,
  })
    .onOk(() => {
      BankTransactionService.delete(id)
        .then(() => {
          $q.notify({
            message: 'Deleted',
            icon: 'announcement',
          });
          router.back();
        })
        .catch(err => {
          $q.notify({
            type: 'negative',
            message: t(err.response.data.message),
          });
        });
    })
    .onCancel(() => {
      console.log('>>>> Cancel');
    })
    .onDismiss(() => {
      console.log('I am triggered on both OK and Cancel');
    });
};
</script>
