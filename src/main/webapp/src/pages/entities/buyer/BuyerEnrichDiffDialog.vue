<template>
  <q-dialog :model-value="modelValue" @update:model-value="val => $emit('update:modelValue', val)" persistent>
    <q-card style="min-width: 350px">

      <q-toolbar>
        <div class="text-h6">{{ t('afaktoApp.buyer.enrichedChangesTitle') }}</div>
        <q-space />
        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
        </q-toolbar>

      <q-card-section>
        <div v-if="differences.length">
          <div class="row justify-around items-stretch q-gutter-md">
            <div class="col">
              <address-comp :model-value="originalAddress" :readonly="true" />
            </div>

            <div class="col-auto flex items-center">
              <q-icon name="arrow_forward" size="xl" class="text-grey-7" />
            </div>

            <div class="col">
              <address-comp :model-value="enrichedAddress" :readonly="false" />
            </div>
          </div>
        </div>
        <div v-else class="text-center text-grey">
          {{ t('afaktoApp.buyer.noChanges') }}
        </div>
      </q-card-section>

      <q-card-actions align="center">
        <q-btn  :label="$t('entity.action.save')" type="submit" icon="label_important" class="buttonBrand"  label="Save" color="primary" @click="onSubmit(entity, enrichedAddress)" :loading="loading" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
const { notifyError } = useNotifications();
const { t } = useI18n();
import { computed, ref } from 'vue';

import AddressComp from 'pages/subcomponents/AddressComp.vue';
import BuyerService from 'pages/entities/buyer/buyer.service';
import useNotifications from 'src/util/useNotifications';

const emit = defineEmits(['update:modelValue']);
const loading = ref(false);

// Method to close the dialog without saving
const closeDialog = () => {
  emit('update:modelValue', false); // This will close the dialog
};

const props = defineProps({
  modelValue: Boolean,
  entity: Object,
  differences: Array,
  originalAddress: Object,
  enrichedAddress: Object,
});

// Submit changes
const onSubmit = async (entity, enrichedAddress) => {
  loading.value = true;
  entity.address = enrichedAddress;
  BuyerService.save(entity)
    .catch(error => notifyError(error))
    .finally(() => {
      loading.value = false;
      closeDialog();
    });
};
</script>
