<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.buyer.home.title') }}</h1>

      <q-space />

      <q-btn v-if="hasRoleWriter && entity.id" class="buttonNeutral" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn class="buttonNeutral" icon="close" to=".">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-tabs v-model="tab" :vertical="$q.platform.is?.desktop">
        <q-tab name="main" :label="t('afaktoApp.buyer.detail.title')" />
        <q-tab name="address" :label="t('afaktoApp.buyer.address')" />
        <q-tab name="contact" :label="t('afaktoApp.buyer.contact')" />
        <q-tab v-if="entity.id" name="invoices" :label="t('afaktoApp.buyer.detail.invoices')" />
        <q-tab v-if="entity.id" name="insurer" :label="t('afaktoApp.buyer.detail.insurer')" />
        <q-tab v-if="entity.buyerFromFactor" name="factor" :label="t('afaktoApp.buyer.detail.factor')" />
      </q-tabs>

      <q-tab-panels v-model="tab" animated swipeable :vertical="$q.platform.is?.desktop">
        <q-tab-panel name="main">
          <q-select
            v-model="entity.company"
            class="required"
            filled
            :label="$t('afaktoApp.buyer.company')"
            :options="useAuthenticationStore().account.companies"
            option-label="name"
            option-value="id"
            :readonly="!hasRoleWriter || !!entity.id"
            :rules="[required]"
          />

          <b-input
            v-model="entity.code"
            class="required"
            :label="$t('afaktoApp.buyer.code')"
            :readonly="!hasRoleWriter"
            :rules="[required]"
          />
          <b-input
            v-model="entity.name"
            class="required"
            :label="$t('afaktoApp.buyer.name')"
            :loading="searchByIdentifier"
            :readonly="!hasRoleWriter"
            :rules="[required]"
            @change="checkSelectedName"
          />

          <q-input
            v-model="entity.number"
            class="required"
            :fill-mask="['SIREN', 'SIRET'].includes(entity.numberType)"
            filled
            :label="$t('afaktoApp.buyer.number')"
            :mask="NUMBER_TYPE_MASKS[entity.numberType]"
            :loading="searchByIdentifier"
            :readonly="!hasRoleWriter"
            :rules="[required]"
            unmasked-value
            @change="checkSelectedType"
          >
            <template #prepend>
              <q-select
                v-model="entity.numberType"
                bg-color="transparent"
                class="required"
                :label="$t('afaktoApp.buyer.numberType')"
                :options="NUMBER_TYPES"
                :option-label="numberType => $t(`afaktoApp.NumberType.${numberType}`).replaceAll('_', ' ')"
                :readonly="!hasRoleWriter"
                :rules="[required]"
                style="min-width: 12em"
              />
            </template>
          </q-input>

          <q-toggle
            v-model="entity.excluded"
            :label="$t('afaktoApp.buyer.excluded') + ' - ' + $t('afaktoApp.buyer.excluded_help')"
            :disable="!hasRoleWriter"
          />
          <q-icon color="warning" right name="remove_done" size="sm" style="vertical-align: baseline" />
          <q-input
            v-if="entity.excluded"
            v-model="entity.exclusionReason"
            filled
            :label="t('afaktoApp.buyer.exclusionReason')"
            :readonly="!hasRoleWriter"
            rows="2"
            type="textarea"
          />
        </q-tab-panel>

        <q-tab-panel name="address">
          <address-comp v-model="entity.address" :readonly="!hasRoleWriter" />
        </q-tab-panel>

        <q-tab-panel name="contact">
          <contact-comp v-model="entity.contact" :readonly="!hasRoleWriter" />
        </q-tab-panel>

        <q-tab-panel name="invoices">
          <buyer-details-invoices />
        </q-tab-panel>

        <q-tab-panel name="insurer">
          <contract-insurance-table v-if="!entity.buyerFromInsurer" v-model="entity" :entity="entity" />
          <buyer-details-from-insurer v-else v-model="entity" />

          <b-input
            class="q-mt-lg"
            v-if="entity.creditLimit && entity.creditLimit.amount != null && !entity.buyerFromInsurer"
            input-class="text-right"
            :label="$t('afaktoApp.buyer.creditLimit')"
            :model-value="$n(entity.creditLimit.amount, 'currencyCode', { currency: entity.creditLimit.currency })"
            readonly
          >
            <template #append>
              <q-icon v-if="entity.balance > entity.creditLimit.amount" color="warning" name="warning" size="md">
                <q-tooltip>{{ $t('afaktoApp.buyer.incoherentBalance_helper') }}</q-tooltip>
              </q-icon>
            </template>
            <entity-meta-dates v-if="entity.creditLimit" :entity="entity.creditLimit" />
          </b-input>
          <credit-limit-requests-table v-model="entity" @credit-limit-requested="creditLimitRequested" />
        </q-tab-panel>

        <q-tab-panel name="factor">
          <b-input :model-value="entity.buyerFromFactor?.factorCode" :label="$t('afaktoApp.buyer.buyerFromFactor.factorCode')" readonly />
          <b-input :label="$t('afaktoApp.buyer.buyerFromFactor.number')" :model-value="entity.buyerFromFactor.number" readonly />

          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.buyer.buyerFromFactor.amountApproved')"
            :model-value="$n(entity.buyerFromFactor.amountApproved || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
            readonly
          >
            <template #append>
              <q-icon v-if="entity.creditLimit?.amount != entity.buyerFromFactor.amountApproved" color="warning" name="warning" size="md">
                <q-tooltip>{{ $t('afaktoApp.buyer.incoherentLimit_helper') }}</q-tooltip>
              </q-icon>
            </template>
          </b-input>

          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.buyer.buyerFromFactor.amountOutstanding')"
            :model-value="$n(entity.buyerFromFactor?.amountOutstanding || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
            readonly
          >
            <template #append>
              <q-icon
                v-if="entity.buyerFromFactor.amountOutstanding > entity.buyerFromFactor.amountApproved"
                color="warning"
                name="warning"
                size="md"
              >
                <q-tooltip>{{ $t('afaktoApp.buyer.incoherentAmount_helper') }}</q-tooltip>
              </q-icon>
            </template>
          </b-input>
          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.buyer.buyerFromFactor.amountFunded')"
            :model-value="$n(entity.buyerFromFactor.amountFunded || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
            readonly
          />
          <b-input
            input-class="text-right"
            :label="$t('afaktoApp.buyer.buyerFromFactor.amountSecured')"
            :model-value="$n(entity.buyerFromFactor.amountSecured || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
            readonly
          />

          <entity-meta-dates :entity="entity.buyerFromFactor" />
        </q-tab-panel>
      </q-tab-panels>

      <div align="center" class="full-width">
        <q-btn v-if="hasRoleWriter" class="buttonBrand" icon="label_important" :label="$t('entity.action.save')" type="submit" />
      </div>
    </q-form>

    <entity-meta :entity="entity" />
  </q-page>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import BuyerService from 'pages/entities/buyer/buyer.service';
import { required } from 'src/components/rules';
import { NUMBER_TYPES, NUMBER_TYPE_MASKS } from 'src/constants/numberType';
import EntityMeta from 'src/pages/subcomponents/EntityMeta.vue';
import EntityMetaDates from 'src/pages/subcomponents/EntityMetaDates.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';
import AddressComp from '../../subcomponents/AddressComp.vue';
import ContactComp from '../../subcomponents/ContactComp.vue';
import BuyerDetailsFromInsurer from './BuyerDetailsFromInsurer.vue';
import BuyerDetailsInvoices from './BuyerDetailsInvoices.vue';
import CreditLimitRequestsTable from '../credit-limit-request/CreditLimitRequestsTable.vue';
import ContractInsuranceTable from '../insurance/ContractInsuranceTable.vue';

const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { notifyError, notifyCustomError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const tab = ref(route.query.tab || 'main');

const entity = ref({
  address: { city: '' },
  contact: { name: '' },
});

onMounted(async () => {
  if (route.params.id) {
    try {
      entity.value = await BuyerService.get(route.params.id);
    } catch (error) {
      notifyError(error);
    }
  }

  setupBeforeUnload(t, document.forms[0], entity.value);
});

const creditLimitRequested = creditLimit => {
  if ('ACCEPTED' != creditLimit.status) return;

  // So that submit button will not change state
  const dirtyForm = Array.from(document.forms).find(form => form.isDirty);
  entity.value.creditLimit = creditLimit;
  if (!dirtyForm) setTimeout(() => setupBeforeUnload(t, document.forms[0], entity.value), 100);
};

const searchByIdentifier = ref(false);

const onGetLegalEntityByIdentifier = () => {
  searchByIdentifier.value = true;

  BuyerService.enrich(entity.value)
    .then(enrichedBuyer => {
      entity.value.name = enrichedBuyer.data.buyer.name;
      entity.value.numberType = enrichedBuyer.data.buyer.numberType;
      entity.value.number = enrichedBuyer.data.buyer.number;
      entity.value.address = enrichedBuyer.data.buyer.address;
    })
    .catch(err => {
      if (err.response?.status === 400 && err.response.data?.errorCode)
        notifyCustomError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
      else
        notifyError(err);
    })
    .finally(() => (searchByIdentifier.value = false));
};

const checkSelectedName = () => {
  if (entity.value.number?.length !== 0)
    return;
  onGetLegalEntityByIdentifier();
}

const checkSelectedType = () => {
  if (entity.value.numberType === 'SIREN' && entity.value.number.length !== 9)
    return
  onGetLegalEntityByIdentifier();
};

const onSubmit = async () => {
  BuyerService.save(entity.value)
    .then(() => router.back())
    .catch(error => notifyError(error));
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.buyer.delete.question', { id: entity.value.name }),
    cancel: true,
  }).onOk(() => {
    BuyerService.delete(entity.value.id)
      .then(() => {
        $q.notify({
          message: 'Deleted',
          icon: 'announcement',
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};
</script>
