<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.buyer.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <q-btn class="buttonNeutral" v-if="hasRoleWriter" icon="o_file_upload" :label="$t('entity.action.import')" to="/buyers/upload" />

        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" v-if="hasRoleAdmin" icon="add" label="new add buyer"  @click.stop="createBuyer()"/>
        <q-btn class="buttonBrand" v-if="hasRoleWriter" icon="add" :label="$t('afaktoApp.buyer.home.createLabel')" to="/buyers/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns.filter(col => 'hasCover' != col.name)" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_event, { id }) => router.push(`/buyers/${id}`)"
    >
      <template #body-cell-createdDate="props">
        <q-td :props="props">
          {{ $d(new Date(props.row.createdDate)) }}
          <q-tooltip>{{ $d(new Date(props.row.createdDate), 'long') }}</q-tooltip>
        </q-td>
      </template>
      <template #header-cell-address_country>
        <q-th class="address_country" />
      </template>
      <template #header-cell-warnings>
        <q-th class="boolean" />
      </template>

      <template #body-cell-address_country="props">
        <q-td class="address_country" :props="props">
          <template v-if="props.row.address?.country">
            <em :class="`fi fi-${props.row.address.country.toLowerCase()} rounded-borders`" />
            <q-tooltip>{{ countryNames.of(props.row.address.country.toUpperCase()) }}</q-tooltip>
          </template>
          <template v-else-if="canEnrich()">
            <q-btn
              v-if="isEnrichable(props.row).enrichable"
              style="margin: 0 -12px; padding: 0 12px"
              icon="auto_fix_high"
              class="enrichButton"
              @click.stop="enrichBuyer(props.row)"
            >
              <q-tooltip>{{ t('afaktoApp.buyer.enrichBuyer') }}</q-tooltip>
            </q-btn>
            <q-btn
              v-else
              style="margin: 0 -12px; padding: 0 12px"
              icon="auto_fix_high"
              class="enrichButton"
              disable
            >
              <q-tooltip>{{ $t(`afaktoApp.buyer.enrichNotAvailable.${isEnrichable(props.row).reason}`) }}</q-tooltip>
            </q-btn>
          </template>
        </q-td>
      </template>
      <template #body-cell-numberType="props">
        <q-td class="numberType" :props="props">
          <q-badge color="blue">{{ $t(`afaktoApp.NumberType.${props.row.numberType}`).replaceAll('_', ' ') }}</q-badge>
        </q-td>
      </template>
      <template #body-cell-number="props">
        <q-td :props="props">
          <template
            v-if="props.row.number && props.row.numberType === 'SIREN' && /^\d+$/.test(props.row.number) && props.row.number.length == 9"
          >
            {{ parseInt(props.row.number).toLocaleString('fr') }}
          </template>
          <template v-else>{{ props.row.number }}</template>
        </q-td>
      </template>
      <template #body-cell-warnings="props">
        <q-td class="boolean">
          <q-icon
            v-if="props.value"
            color="warning"
            name="warning"
            size="sm">
            <q-tooltip style="white-space: pre-line">
              <ul style="margin: 0; padding-left: 20px;">
                <li v-if="props.row.excluded">
                  {{ $t('afaktoApp.buyer.excluded') }}
                  <ul v-if="props.row.exclusionReason">
                    <li>
                      {{ props.row.exclusionReason }}
                    </li>
                  </ul>
                </li>
                <li v-if="props.row.buyerFromFactorUnknown">{{ $t('afaktoApp.buyer.buyerFromFactorUnknown_helper') }}</li>
                <li v-if="hasIncoherentLimit(props.row)">{{ $t('afaktoApp.buyer.incoherentLimit_helper') }}</li>
                <li v-if="hasIncoherentBalance(props.row)">{{ $t('afaktoApp.buyer.incoherentBalance_helper') }}</li>
                <li v-if="hasIncoherentAmount(props.row)">{{ $t('afaktoApp.buyer.incoherentAmount_helper') }}</li>
              </ul>
            </q-tooltip>
          </q-icon>
        </q-td>
      </template>

      <template #body-cell-gauge="props">
        <q-td class="gauge" :props="props" width="200">
          <template v-if="props.row.buyerFromFactor?.amountApproved">
            <apexchart height="20" :options="options" :series="getSeries(props.row)" type="bar" />
          </template>
        </q-td>
      </template>
    </q-table>
    <buyer-creation
      v-model="showBuyerCreation"
      v-if="showBuyerCreation"
      :entity="newBuyer"
    />
    <buyer-enrich-diff-dialog
      v-model="showEnrichDialog"
      v-if="showEnrichDialog"
      :differences="changedFields"
      :original-address="originalAddress"
      :enriched-address="enrichedAddress"
      :entity="selectedBuyer"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { getCssVar } from 'quasar';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import BuyerService from 'pages/entities/buyer/buyer.service';
import BuyerEnrichDiffDialog from 'pages/entities/buyer/BuyerEnrichDiffDialog.vue';
import { filtersToQueryParams } from '/src/util/filtering';
import { NUMBER_TYPES } from 'src/constants/numberType';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import useNotifications from 'src/util/useNotifications';
import BuyerCreation from "pages/entities/buyer/BuyerCreation.vue";
import {useBuyersListStore} from "src/stores/buyers-list-store.js";

const baseApiUrl = '/api/buyers';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { n, t } = useI18n();
const { notifyError, notifyCustomError } = useNotifications();
const route = useRoute();
const router = useRouter();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.company?.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name })),
    },
    label: t('afaktoApp.buyer.company'),
    name: 'company.name',
    sortable: true,
  },
  {
    filter: { type: 'boolean' },
    label: t('afaktoApp.invoice.hasCover'),
    hidden: true,
    name: 'hasCover',
  },
  {
    align: 'left',
    field: 'code',
    filter: { type: 'text' },
    label: t('afaktoApp.buyer.code'),
    name: 'code',
    sortable: true,
  },
  {
    align: 'left',
    field: row => row.address?.country,
    filter: { type: 'country' },
    label: t('afaktoApp.address.country'),
    name: 'address_country',
    sortable: true,
  },
  {
    align: 'left',
    field: 'name',
    filter: { type: 'text' },
    label: t('afaktoApp.buyer.name'),
    name: 'name',
    sortable: true,
  },
  {
    field: 'numberType',
    filter: {
      type: 'enum',
      values: NUMBER_TYPES.map(numberType => ({
        label: t(`afaktoApp.NumberType.${numberType}`).replaceAll('_', ' '),
        value: numberType,
      })),
    },
    headerClasses: 'numberType',
    label: t('afaktoApp.buyer.numberType'),
    name: 'numberType',
    sortable: true,
  },
  {
    align: 'left',
    field: 'number',
    filter: { type: 'text' },
    label: t('afaktoApp.buyer.buyerID'),
    name: 'number',
    sortable: true,
  },
  {
    align: 'center',
    field: 'createdDate',
    label: t('global.field.createdDate'),
    name: 'createdDate',
    sortable: true,
  },
  {
    field: row => hasAnyWarning(row),
    filter: false,
    label: t('global.field.warnings'),
    name: 'warnings',
    sortable: true
  },
  {
    field: row => row.excluded,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.excluded'),
    name: 'excluded',
    sortable: true,
    hidden: true
  },
  {
    field: 'currency',
    filter: { type: 'currency' },
    label: t('afaktoApp.buyer.currency'),
    name: 'currency',
    sortable: true,
  },
  {
    classes: 'text-positive',
    field: row => row.creditLimit?.amount,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.creditLimit?.currency })),
    label: t('afaktoApp.buyer.creditLimit'),
    name: 'creditLimit_amount',
    sortable: true,
  },
  {
    field: row => row.buyerFromFactorUnknown,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.buyerFromFactorUnknown'),
    name: 'buyerFromFactorUnknown',
    sortable: true,
    hidden: true
  },
  {
    classes: row => (row.buyerFromFactor?.amountApproved < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.buyerFromFactor?.amountApproved,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    hidden: true,
    label: t('afaktoApp.buyer.buyerFromFactor.amountApproved'),
    name: 'buyerFromFactor_amountApproved',
    sortable: true,
  },
  {
    field: row => row.buyerFromFactor && row.creditLimit?.amount != row.buyerFromFactor?.amountApproved,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.incoherentLimit'),
    name: 'incoherentLimit',
    sortable: true,
    hidden: true
  },
  {
    classes: row => (row.balance < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.balance,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    label: t('afaktoApp.buyer.balance'),
    name: 'balance',
    sortable: true,
  },
  {
    field: row => row.creditLimit && row.balance > row.creditLimit.amount,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.incoherentBalance'),
    name: 'incoherentBalance',
    sortable: true,
    hidden: true
  },
  {
    classes: row => (row.buyerFromFactor?.amountOutstanding < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.buyerFromFactor?.amountOutstanding,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    hidden: true,
    label: t('afaktoApp.buyer.buyerFromFactor.amountOutstanding'),
    name: 'buyerFromFactor_amountOutstanding',
    sortable: true,
  },
  {
    field: row => row.buyerFromFactor && row.buyerFromFactor.amountOutstanding > row.buyerFromFactor.amountApproved,
    filter: { type: 'boolean' },
    label: t('afaktoApp.buyer.incoherentAmount'),
    name: 'incoherentAmount',
    sortable: true,
    hidden: true
  },
  {
    classes: row => (row.buyerFromFactor?.amountFunded < 0 ? 'text-negative' : 'text-positive'),
    field: row => row.buyerFromFactor?.amountFunded,
    filter: { type: 'number' },
    format: (value, row) => (value == null ? '' : n(value, 'currency', { currency: row.buyerFromFactor?.currency })),
    label: t('afaktoApp.buyer.buyerFromFactor.amountFunded'),
    name: 'buyerFromFactor_amountFunded',
    sortable: true,
  },
  {
    align: 'left',
    filter: false,
    label: t('afaktoApp.buyer.buyerFromFactor.gauge'),
    name: 'gauge',
  },
]);

const visibleColumns = ref([]);
const filters = ref({});
const store = useAuthenticationStore();
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15,
});

const rows = ref([]);

const buyersListStore = useBuyersListStore();

watch(
  () => buyersListStore.updated,
  val => {
    if (val) {
      onRequest({ pagination: pagination.value });
      buyersListStore.clearUpdate();
    }
  },
);

const onRequest = async ({ pagination: { page, rowsPerPage, sortBy, descending } }) => {
  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      ...filtersToQueryParams(filters.value),
    },
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};

const options = {
  chart: {
    background: 'transparent',
    sparkline: {
      enabled: true,
    },
    stacked: true,
    stackType: '100%',
  },
  colors: [getCssVar('accent'), getCssVar('positive')],
  plotOptions: {
    bar: {
      borderRadius: 8,
      horizontal: true,
      colors: {
        backgroundBarColors: ['transparent'],
        backgroundBarOpacity: 0,
        backgroundBarRadius: 10,
      },
    },
  },
  tooltip: {
    x: {
      show: false,
    },
    y: {
      formatter: function (_value, { seriesIndex, w }) {
        return n(w.config.series[seriesIndex].value, 'currencyCode', { currency: w.config.series[seriesIndex].currency });
      },
    },
  },
};

const getSeries = buyer => [
  {
    currency: buyer.buyerFromFactor.currency,
    data: [buyer.buyerFromFactor.amountOutstanding],
    name: t('afaktoApp.buyer.buyerFromFactor.amountOutstanding'),
    value: buyer.buyerFromFactor.amountOutstanding,
  },
  {
    currency: buyer.buyerFromFactor.currency,
    data: [buyer.buyerFromFactor.amountApproved - buyer.buyerFromFactor.amountOutstanding],
    name: t('afaktoApp.buyer.buyerFromFactor.amountApproved'),
    value: buyer.buyerFromFactor.amountApproved,
  },
];

const countryNames = new Intl.DisplayNames([navigator.language], { type: 'region' });

const showBuyerCreation = ref(false);
const showEnrichDialog = ref(false);
const selectedBuyer = ref(null);
const newBuyer = ref(null);
const originalAddress = ref();
const enrichedAddress = ref();
const changedFields = ref([]);

function canEnrich() {
  return hasRoleAdmin;
}

function isEnrichable(buyer) {
  if (!buyer.numberType || !buyer.number)
    return { enrichable: false, reason: 'NO_NUMBER' };

  if (buyer.numberType === 'DUNS_BRADSTREET')
    return { enrichable: false, reason: 'DUNS_UNSUPPORTED' };

  if (buyer.numberType === 'SIREN')
    if (buyer.number.length !== 9 || !/^\d+$/.test(buyer.number))
      return { enrichable: false, reason: 'SIREN_INVALID' };

  if (buyer.numberType === 'VAT')
    if (!/^[A-Z]{2}[A-Z0-9]{6,}$/.test(buyer.number))
      return { enrichable: false, reason: 'VAT_INVALID' };

  return { enrichable: true };
}

// Helper functions for warnings
function hasIncoherentLimit(row) {
  return row.buyerFromFactor && row.creditLimit?.amount != row.buyerFromFactor?.amountApproved;
}

function hasIncoherentBalance(row) {
  return row.creditLimit && row.balance > row.creditLimit.amount;
}

function hasIncoherentAmount(row) {
  return row.buyerFromFactor && row.buyerFromFactor.amountOutstanding > row.buyerFromFactor.amountApproved;
}

function hasAnyWarning(row) {
  return row.excluded || row.buyerFromFactorUnknown || hasIncoherentLimit(row) || hasIncoherentBalance(row) || hasIncoherentAmount(row);
}

async function createBuyer() {
  newBuyer.value = {
    address: { city: '' },
    contact: { name: '' },
  };
  showBuyerCreation.value = true;
}

async function enrichBuyer(buyer) {
  selectedBuyer.value = buyer;
  try {
    const res = await BuyerService.enrich(buyer);
    const enriched = res.data.buyer;
    const diffs = [];

    const fields = ['streetName', 'streetNumber', 'postalCode', 'city', 'stateProvince', 'country'];

    if (buyer.address == null) buyer.address = { city: '' };

    for (const field of fields) {
      const oldVal = buyer.address?.[field] ?? '';
      const newVal = enriched.address?.[field] ?? null;

      if (newVal !== null && oldVal !== newVal) diffs.push({ field });
    }

    changedFields.value = diffs;
    enrichedAddress.value = enriched.address;
    originalAddress.value = buyer.address;
    showEnrichDialog.value = true;
  } catch (err) {
    if (err.response?.status === 400 && err.response.data?.errorCode)
      notifyCustomError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
    else
      notifyError(err);
  }
}
</script>
