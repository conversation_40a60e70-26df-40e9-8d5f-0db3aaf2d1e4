<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.categoryToAccount.home.title') }}</h1>

      <q-space />

      <rows-export
        :base-api-url="baseApiUrl"
        :columns="columns"
        :filters="filters"
        :pagination="pagination"
        :visible-columns="visibleColumns"
      />
      <q-btn color="primary" icon="add" :label="$t('entity.action.add')" to="/category-to-accounts/new" />
      <q-btn color="primary" icon="close" to="/bank-transactions">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_event, { id }) => router.push(`/category-to-accounts/${id}`)"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import useNotifications from 'src/util/useNotifications';
import { BANK_TRANSACTION_TYPE } from '/src/constants/bankTransaction';
import { filtersToQueryParams } from '/src/util/filtering';
import { format } from '/src/util/format';

const baseApiUrl = '/api/category-to-accounts';
const { t } = useI18n();
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.company.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name })),
    },
    label: t('afaktoApp.categoryToAccount.company'),
    name: 'company.name',
    sortable: true,
  },
  {
    align: 'left',
    field: 'currency',
    filter: { type: 'currency' },
    label: t('afaktoApp.bankTransaction.currency'),
    name: 'currency',
    sortable: true,
  },
  {
    align: 'left',
    classes: 'text-capitalize',
    field: 'category',
    filter: {
      type: 'enum',
      values: BANK_TRANSACTION_TYPE.map(category => ({
        label: category.replace(/_/g, ' ').toLowerCase(),
        value: category,
      })),
    },
    format: value => value?.replace(/_/g, ' ')?.toLowerCase(),
    label: t('afaktoApp.categoryToAccount.category'),
    name: 'category',
    sortable: true,
  },
  {
    align: 'left',
    field: 'ledgerAccount',
    filter: { type: 'text' },
    label: t('afaktoApp.categoryToAccount.ledgerAccount'),
    name: 'ledgerAccount',
    sortable: true,
  },
  {
    align: 'left',
    field: 'comment',
    filter: { type: 'text' },
    label: t('afaktoApp.categoryToAccount.comment'),
    name: 'comment',
    sortable: true,
  },
  {
    align: 'left',
    field: 'createdDate',
    filter: { type: 'date' },
    format: value => format(value),
    label: t('global.field.createdDate'),
    name: 'createdDate',
    sortable: true,
  },
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'company.name',
  descending: route.query.descending === 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: Number.parseInt(route.query.rowsPerPage) || 15,
  rowsNumber: 15,
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        ...filtersToQueryParams(filters.value),
      },
    });
    rows.value = response.data;

    pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
    router.replace({ query: { page, sortBy, descending, rowsPerPage } });
  } catch (err) {
    notifyError(err);
  }
};
</script>
