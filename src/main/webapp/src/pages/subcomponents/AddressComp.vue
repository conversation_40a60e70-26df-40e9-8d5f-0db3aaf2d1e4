<template>
  <b-input
    v-model="modelValue.streetNumber"
    :label="$t('afaktoApp.address.streetNumber')"
    :readonly="readonly"
  />
  <b-input
    v-model="modelValue.streetName"
    :label="$t('afaktoApp.address.streetName')"
    :readonly="readonly"
  />
  <b-input
    v-model="modelValue.postalCode"
    :label="$t('afaktoApp.address.postalCode')"
    :readonly="readonly"
  />
  <b-input
    v-model="modelValue.city"
    :label="$t('afaktoApp.address.city')"
    :readonly="readonly"
  />
  <b-input
    v-model="modelValue.stateProvince"
    :label="$t('afaktoApp.address.stateProvince')"
    :readonly="readonly"
  />

  <q-select
    v-model="modelValue.country"
    emit-value
    filled
    :label="$t('afaktoApp.contract.country')"
    map-options
    :options="countries.filter(c => c.iso)"
    option-label="name"
    option-value="code"
    :readonly="readonly"
  >
    <template v-if="modelValue.country" #prepend>
      <span :class="`fi fi-${modelValue.country} rounded-borders`"></span>
    </template>
    <template #option="scope">
      <q-item v-bind="scope.itemProps" dense>
        <q-item-section side>
          <span :class="`fi fi-${scope.opt.code} rounded-borders`" />
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ scope.opt.name }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
import countries from 'flag-icons/country.json';

defineModel({ type: Object });

defineProps({
  readonly: Boolean,
});
</script>
