<template>
  <q-select
    v-model="theModel"
    :filled="filled"
    :label="label"
    :loading="loading"
    map-options
    :multiple="multiple"
    :options="options"
    :option-label="optionLabel"
    options-selected-class="text-bold"
    :option-value="optionValue"
    :rules="rules"
    use-input
    @filter="filterFn"
  />
</template>

<script lang="ts" setup>
import { api } from 'boot/axios';
import { computed, onMounted, PropType, ref } from 'vue';

import useNotifications from 'src/util/useNotifications';

const props = defineProps({
  filled: {
    type: Boolean,
    default: true,
  },
  label: {
    type: String,
    required: false,
    default: '',
  },
  modelValue: {
    type: Object, // Type Annotation
    default: () => null,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  optionLabel: {
    type: [String, Function] as PropType<string | ((option: any) => string)>,
    required: true,
  },
  optionValue: {
    type: [String, Function] as PropType<string | ((option: any) => any)>,
    default: (option: String | Function) => option,
  },
  optionsPreload: {
    type: Boolean,
    default: false,
  },
  rules: {
    type: Object as PropType<any[]>,
    default: null,
  },
  url: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);
const { notifyError } = useNotifications();

const theModel = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
});

const loading = ref();
const allOptions = ref([]);
const options = ref([]);

const loadAllOptions = async () => {
  loading.value = true;
  try {
    allOptions.value = (await api.get(props.url, {})).data ?? [];
    options.value = allOptions.value;
  } catch (error: any) {
    notifyError(error);
  } finally {
    loading.value = false;
  }
};

const filterFn = async (val: string, update: Function) => {
  if (!allOptions.value.length) await loadAllOptions();

  const filteredOptions = allOptions.value.filter(option => {
    const label = typeof props.optionLabel === 'function' ? props.optionLabel(option) : option[props.optionLabel];
    return label?.toLowerCase()?.includes(val.toLowerCase());
  });

  update(() => (options.value = filteredOptions));
};

onMounted(async () => {
  if (props.optionsPreload) loadAllOptions();
});
</script>
