import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

export default function useNotifications() {
  const $q = useQuasar();
  const { t } = useI18n();

  const notifyCustomError = (errorMessage) => {
    $q.notify({
      message: errorMessage,
      timeout: 5000,
      multiLine: true,
      progress: true,
      type: 'info',
      position: 'bottom',
      actions: [
        {
          label: t('error.dismiss'),
          color: 'white',
          handler: () => {},
        },
      ],
    });
  };

  const notifyError = err => {
    const rawDetail = err?.response?.data?.detail;

    const title = t('error.messageTitle');
    const detailAvailable = !!rawDetail && !rawDetail?.startsWith('400 BAD_REQUEST');

    const detailId = `error-detail-${Date.now()}`;
    const toggleId = `error-toggle-${Date.now()}`;

    if (detailAvailable) {
      setTimeout(() => {
        const toggle = document.getElementById(toggleId);
        const detail = document.getElementById(detailId);

        if (toggle && detail) {
          toggle.addEventListener('click', e => {
            e.preventDefault();
            const isHidden = detail.style.display === 'none';
            detail.style.display = isHidden ? 'block' : 'none';
            toggle.innerText = isHidden ? t('error.hideDetails') : t('error.showDetails');
          });
        }
      }, 100);
    }

    const spoiler = detailAvailable
      ? `
        <div>
          <a href="#" id="${toggleId}" style="color: inherit; text-decoration: underline;">
            ${t('error.showDetails')}
          </a>
          <div id="${detailId}" style="display: none; margin-top: 0.5em; font-size: 1em; white-space: pre-wrap;">
            ${rawDetail}
          </div>
        </div>
      `
      : '';

    $q.notify({
      message: title,
      caption: spoiler,
      html: true,
      multiLine: true,
      progress: true,
      type: 'info',
      timeout: 8000,
      position: 'bottom',
      actions: [
        {
          label: t('error.dismiss'),
          color: 'white',
          handler: () => {},
        },
      ],
    });
  };

  return { notifyError, notifyCustomError };
}
