{"afaktoApp": {"invoice": {"home": {"title": "Invoices", "refreshListLabel": "Refresh list", "createLabel": "Add receivable", "createOrEditLabel": "Create or edit an invoice", "search": "Search for invoice", "notFound": "No invoice found"}, "created": "Invoice { param } created", "updated": "Invoice { param } updated", "deleted": "Invoice { param } deleted", "delete": {"question": "Are you sure you want to delete invoice { id }?"}, "detail": {"title": "Invoice", "cession": "Cession", "factor": "Factor"}, "upload": {"title": "Import", "subtitle": "Choose one or more files", "samples": "Samples of supported files", "created": "Created invoices: { count }", "updated": "Updated invoices: { count }", "headers": "Sample headers", "headers_help": "The first row of the file must contain the headers, their order is not important. The supported headers are:"}, "invoiceFromFactor": {"amount": "Amount", "balance": "Balance", "amountDraftReceived": "Draft received", "draftDueDate": "Draft due date", "amountFunded": "Funded amount", "amountUnfunded": "Unfunded amount", "amountSecured": "Guaranted amount", "amountUnsecured": "Unavailable amount"}, "id": "ID", "buyer": "Buyer", "buyerCode": "Buyer code", "type": "Type", "invoiceNumber": "Invoice number", "date": "Invoice date", "dueDate": "Due date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "balance": "Balance", "hasCover": "Has cover", "underFactor": "Under factor", "reconciliationJournal": "Reconciliation journal", "gap": "Gap", "gapType": "Gap type", "gapType_values": {"EXCLUSION": "Exclusion", "OVERDUE": "Overdue", "RECONCILIATION": "Reconciliation"}, "gapCredit": "Direction", "gapCredit_values": {"true": "Creditor", "false": "Debitor"}, "wrongCurrency": "This currency is not available in your factor contracts"}}}