{"afaktoApp": {"buyer": {"home": {"title": "Buyers", "titleCreate": "Create a new buyer", "refreshListLabel": "Refresh list", "createLabel": "Add buyer", "createOrEditLabel": "Create or edit a buyer", "search": "Search for buyer", "notFound": "No buyers found"}, "created": "A new buyer is created with identifier { param }", "updated": "A buyer is updated with identifier { param }", "deleted": "A buyer is deleted with identifier { param }", "delete": {"question": "Are you sure you want to delete buyer { id }?"}, "create": {"basicInformation": "Basic information", "enrichTooltip": "Find a buyer using the company name or SIRET to automatically fill in the following fields.", "onlineData": "Online Data", "companyExample": "eg: CARREFOUR", "companyHint": "The company the buyer is attached to", "countryExample": "eg: THE NETHERLANDS", "countryHint": "The country of the buyer", "buyerDetails": "Buyer details", "noMatchingCompany": "No matching company found", "manuallyFill": "Fill Manually"}, "detail": {"title": "Buyer", "invoices": "Invoices", "insurer": "Insurer", "factor": "Factor"}, "upload": {"title": "Import", "subtitle": "Choose one or more files", "samples": "Samples of supported files", "created": "Created buyers: { count }", "updated": "Updated buyers: { count }", "headers": "Sample headers", "headers_help": "The first row of the file must contain the headers, their order is not important. The supported headers are:"}, "buyerFromInsurer": {"title": "Data from insurer", "updatedCreditLimit": "Credit limit updated", "noChange": "The credit limit has not changed", "obsolete": "This limit is obsolete, the amount used by afakto is 0."}, "buyerFromFactor": {"title": "Data from factor", "factorCode": "Factor code", "number": "Number", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amountApproved": "Amount approved", "noAmountApproved": "No credit limit", "amountOutstanding": "Outstanding (factor)", "amountFunded": "Funded", "amountSecured": "Secured", "gauge": "Gauge"}, "id": "ID", "company": "Company", "code": "Code", "name": "Name", "buyerID": "Buyer ID", "country": "Country", "currency": "<PERSON><PERSON><PERSON><PERSON>", "outstanding": "Outstanding", "balance": "Outstanding (client)", "creditLimit": "Credit Limit", "numberType": "ID type", "number": "Legal ID number", "enrichBuyer": "Enrich Buyer", "enrichNotAvailable": {"NO_NUMBER": "Your buyer does not have a legal id", "DUNS_UNSUPPORTED": "Enrichment with a DUNS id is not currently supported", "SIREN_INVALID": "Your buyer does not have a valid SIREN number", "VAT_INVALID": "Your buyer does not have a valid VAT number"}, "enrichmentErrors": {"NO_CONTRACT": "Your company does not have any contract letting you enrich your buyers", "NO_SUPPORTED_CONTRACT": "Your company does not have any contract supporting buyer enrichment", "SIREN_NAME_NOT_FOUND": "The buyer name does not have any known SIREN number", "SIREN_INVALID": "The SIREN number is not valid", "SIREN_NOT_FOUND": "The SIREN number didn't match any company", "VAT_INVALID": "The VAT number is not valid", "VAT_INVALID_COUNTRY": "The country on the VAT number is incorrect", "NOT_FOUND": "The buyer's number didn't match any company", "COUNTRY_NOT_ALLOWED": "Your contract does not let you search for the buyer's country."}, "enrichedChangesTitle": "Enrichment", "noChanges": "No changes", "originalAddress": "Original address", "enrichedAddress": "Enriched address", "excluded": "Excluded", "excluded_help": "Invoices will be excluded from future cessions", "exclusionReason": "Exclusion reason", "address": "Address", "contact": "Contact", "paymentTerms": "Payment terms", "invoice": "Invoice", "creditLimitHistory": "Credit limit history", "incoherentLimit": "Incoherent limit", "incoherentLimit_helper": "Insurer's credit limit is different from factor approved amount", "incoherentBalance": "Warning on client outstanding", "incoherentBalance_helper": "Outstanding amount above insurer's credit limit", "incoherentAmount": "Warning on factor outstanding", "incoherentAmount_helper": "Factor outstanding amount above approved amount", "buyerFromFactorUnknown": "Unknown buyer", "buyerFromFactorUnknown_helper": "The factor is missing information about this buyer", "numberWithAddress": "Number of buyers with an address: { count }", "pickAddress": "Pick an address", "numberWithoutAddress": "Number of buyers without an address: { count }"}}}