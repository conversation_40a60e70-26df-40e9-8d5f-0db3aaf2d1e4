import { all } from 'deepmerge';

const jsons = [
  'activate',
  'authority',
  'address',
  'bankTransaction',
  'buyer',
  'categoryToAccount',
  'cession',
  'comment',
  'company',
  'configuration',
  'contact',
  'contract',
  'creditLimit',
  'creditLimitHistory',
  'creditLimitRequest',
  'creditLimitStatus',
  'dashboard',
  'datastream',
  'datastreamType',
  'datastreamFailure',
  'error',
  'externalCreditInsurance',
  'factorInstitution',
  'global',
  'health',
  'help',
  'home',
  'invoice',
  'invoiceType',
  'login',
  'logs',
  'metrics',
  'numberType',
  'password',
  'paymentTerms',
  'register',
  'reset',
  'sessions',
  'settings',
  'tracker',
  'user',
  'dashboard',
];

const modules = import.meta.glob('./*.json', { eager: true });
export default all(Object.values(modules));
