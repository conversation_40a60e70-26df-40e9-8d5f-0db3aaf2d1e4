package com.afakto.domain;

import java.math.BigDecimal;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.envers.Audited;

import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;

/**
 * A CreditLimit, coming from insurer.
 */
@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class CreditLimit extends BaseEntity {
    private BigDecimal amount;

    private String currency;
}
