package com.afakto.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Formula;

import com.afakto.domain.enumeration.BankTransactionCategory;
import com.afakto.domain.enumeration.BankTransactionType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class BankTransaction extends BaseEntity implements CompanyOwned {
    @JoinColumn(nullable = false, updatable = false)
    @ManyToOne(optional = false)
    private Company company;

    @Column(nullable = false)
    private LocalDate date;
    @Column(nullable = false)
    private LocalDate valueDate;

    private String transactionReferenceNumber;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private BankTransactionType type;

    @Column(nullable = false)
    private String currency;
    @Column(nullable = false)
    private BigDecimal amount;
    private BigDecimal balance;

    @Enumerated(EnumType.STRING)
    private BankTransactionCategory category;

    @Formula("(SELECT cta.ledger_account FROM category_to_account cta WHERE cta.company_id = company_id AND cta.currency = currency AND cta.category = category)")
    private String ledgerAccount;

    private String narrative;

    private String identificationCode;

    private String referenceAccountServiceInstitution;
}
