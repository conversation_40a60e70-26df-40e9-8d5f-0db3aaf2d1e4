package com.afakto.domain;

import java.math.BigDecimal;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import com.afakto.domain.enumeration.NumberType;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class Buyer extends BaseEntity implements CompanyOwned {
    @JoinColumn(nullable = false, updatable = false)
    @ManyToOne(optional = false)
    private Company company;

    @OneToOne(mappedBy = "buyer")
    private BuyerFromFactor buyerFromFactor;
    @OneToOne(mappedBy = "buyer")
    private BuyerFromInsurer buyerFromInsurer;

    @Column(nullable = false)
    private String code;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NumberType numberType;

    @Column(nullable = false)
    private String number;

    private String name;

    private boolean excluded;
    private String exclusionReason;

    // TODO suppression, la validation par pays va pour l'instant dépendre de
    // l'assureur
    // private transient Country country;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(unique = true, nullable = true)
    private Address address;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(unique = true)
    private Contact contact;

    @NotAudited
    @OneToOne(cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    private PaymentTerms paymentTerms;

    // Watch out, this is mostly a cache column, populated from the first invoice
    @Column(updatable = false)
    private String currency;

    // Watch oui, this is a cache column, populated from the invoices balances
    @Column(updatable = false)
    @NotAudited
    private BigDecimal balance;

    @OneToOne(cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    private CreditLimit creditLimit;

    private boolean buyerFromFactorUnknown;
}
