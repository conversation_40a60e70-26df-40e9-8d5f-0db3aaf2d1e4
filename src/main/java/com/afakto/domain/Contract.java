package com.afakto.domain;

import java.time.LocalDate;
import java.util.Set;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.envers.Audited;

import com.afakto.domain.contractdata.FinancialInformation;
import com.afakto.domain.enumeration.ContractStatus;
import com.afakto.domain.enumeration.CreditInsurance;
import com.afakto.domain.enumeration.FactoringProgramType;

import jakarta.persistence.CascadeType;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

/**
 * A Contract.
 */
@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class Contract extends BaseEntity implements CompanyOwned {
    @JoinColumn(nullable = false, updatable = false)
    @ManyToOne(optional = false)
    private Company company;

    private String contractNumber;

    @Enumerated(EnumType.STRING)
    private ContractStatus status;
    private LocalDate signatureDate;
    private LocalDate activationDate;

    private boolean syndicatedProgram;
    private boolean confidentialProgram;
    private boolean withRecourse;

    @Enumerated(EnumType.STRING)
    private FactoringProgramType programType;

    private int monthlyMaxCession;

    // New Definition Of Default
    // Number of days after the due date that invoices are considered as default
    private Integer defaultOverDue;

    private boolean cashIn;
    private String bic;
    private String iban;
    // The accounting journal corresponding to factor records
    private String journal;

    private String country;

    @ElementCollection
    private Set<String> countries;

    @Enumerated(EnumType.STRING)
    private CreditInsurance creditInsurance;

    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    private CreditInsurancePolicy creditInsurancePolicy;

    @JoinColumn(nullable = false, updatable = false)
    @ManyToOne(optional = false)
    private FactorInstitution factorInstitution;

    private String factorAccountNumber;

    @Embedded
    private FinancialInformation financialInformation;
}
