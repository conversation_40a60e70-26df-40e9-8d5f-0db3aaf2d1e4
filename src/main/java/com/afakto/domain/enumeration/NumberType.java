package com.afakto.domain.enumeration;

import static com.afakto.domain.enumeration.Normalizable.normalize;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * The NumberType enumeration.
 */
public enum NumberType {
    COMMERCIAL_REGISTER,
    DUNS_BRADSTREET,
    NATIONAL_REGISTRATION_NUMBER,
    PRO("9PRO"),
    SIREN,
    SIRET,
    VAT("TVA", "TVA-Intracom");

    private final String[] transpositions;

    NumberType(String... transpositions) {
        this.transpositions = transpositions;
    }

    private static final Map<String, NumberType> VALUE_MAP = new HashMap<>();

    static {
        for (var type : values()) {
            VALUE_MAP.put(type.toString(), type);
            for (String value : type.transpositions)
                VALUE_MAP.put(normalize(value), type);
        }
    }

    public static Optional<NumberType> fromValue(String value) {
        return Optional.ofNullable(VALUE_MAP.get(normalize(value)));
    }
}
