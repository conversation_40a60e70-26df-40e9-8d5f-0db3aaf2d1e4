package com.afakto.domain;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.type.SqlTypes;

import com.afakto.domain.enumeration.ActionType;
import com.afakto.domain.enumeration.InvoiceType;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;

/**
 * A Cession.
 */
@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class Cession extends BaseEntity implements CompanyOwned {
    @ManyToOne(optional = false)
    private Contract contract;

    private Integer count;
    private BigDecimal sum;

    @Column(columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<InvoiceType, Map<ActionType, Number[]>> metadata;

    @ManyToMany
    @JoinTable(joinColumns = @JoinColumn(name = "cession_id"), inverseJoinColumns = @JoinColumn(name = "invoice_id"))
    private Set<Invoice> invoices;

    @NotAudited
    @OneToMany(mappedBy = "cession", cascade = CascadeType.ALL)
    private Set<Gap> gaps;

    @ManyToMany(cascade = CascadeType.ALL)
    @NotAudited
    private Set<Datastream> datastreams;

    public Company getCompany() {
        return contract.getCompany();
    }

    /**
     * Calculate numbers and sums, for each invoice type
     */
    public Cession calc(Set<Invoice> toRemove) {
        count = invoices.size();
        sum = invoices.stream().map(Invoice::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

        // Group invoices by type and factor status
        var groupedInvoices = invoices.stream().collect(Collectors.groupingBy(
                Invoice::getType,
                Collectors.partitioningBy(Invoice::isUnderFactor)));

        // Group invoices to remove by type
        var groupedToRemove = toRemove.stream().collect(Collectors.groupingBy(Invoice::getType));

        // Setup metadata
        metadata = Arrays.stream(InvoiceType.values()).collect(Collectors.toMap(
                type -> type,
                type -> {
                    var insertInvoices = groupedInvoices.getOrDefault(type, Map.of(false, List.of())).get(false);
                    var updateInvoices = groupedInvoices.getOrDefault(type, Map.of(true, List.of())).get(true);
                    var deleteInvoices = groupedToRemove.getOrDefault(type, List.of());

                    return Map.of(
                            ActionType.INSERT, new Number[] {
                                    insertInvoices.size(),
                                    insertInvoices.stream().map(Invoice::getBalance).reduce(BigDecimal.ZERO,
                                            BigDecimal::add),
                            },
                            ActionType.UPDATE, new Number[] {
                                    updateInvoices.size(),
                                    updateInvoices.stream().map(Invoice::getBalance).reduce(BigDecimal.ZERO,
                                            BigDecimal::add),
                            },
                            ActionType.DELETE, new Number[] {
                                    deleteInvoices.size(),
                                    deleteInvoices.stream().map(Invoice::getAmount).reduce(BigDecimal.ZERO,
                                            BigDecimal::add),
                            });
                }));

        return this;
    }
}
