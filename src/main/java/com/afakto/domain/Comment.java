package com.afakto.domain;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

/**
 * A Comment.
 */
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
@Entity
@Getter
@Setter
public class Comment extends BaseEntity implements OrgIdOwned {
    @Column(nullable = false, updatable = false)
    private String orgId;
    @JoinColumn(updatable = false)
    @ManyToOne
    private Company company;
    @ManyToMany
    private Set<User> users = new HashSet<>();

    @Column(nullable = false)
    private UUID relatedEntityId;
    @Column(nullable = false)
    private String relatedEntityType;
    @Column(nullable = false)
    private String text;
}
