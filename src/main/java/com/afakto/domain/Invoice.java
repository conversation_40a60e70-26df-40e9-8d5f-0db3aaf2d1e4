package com.afakto.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.envers.Audited;

import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.domain.enumeration.PaymentMethod;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class Invoice extends BaseEntity implements CompanyOwned {
    @ManyToOne(optional = false)
    private Buyer buyer;

    @OneToOne(mappedBy = "invoice")
    private InvoiceFromFactor invoiceFromFactor;

    @Enumerated(EnumType.STRING)
    private InvoiceType type;
    private String invoiceNumber;

    @Column(name = "invoice_date")
    private LocalDate date;
    private LocalDate dueDate;
    private String currency;
    private BigDecimal amount;
    private BigDecimal balance;

    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod;

    @Column(nullable = false)
    private boolean isUnderFactor;
    private String reconciliationJournal;
    private LocalDate paymentDate;

    @ManyToMany(mappedBy = "invoices")
    private Set<Cession> cessions;

    @OneToOne(mappedBy = "invoice", fetch = FetchType.EAGER, cascade = CascadeType.REMOVE)
    private Gap gap;

    public Company getCompany() {
        return buyer.getCompany();
    }
}
