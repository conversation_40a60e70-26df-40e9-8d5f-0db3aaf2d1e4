package com.afakto.domain;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.afakto.domain.enumeration.BankTransactionCategory;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

/**
 * Manages ledger mappings
 *
 * There are three dimensions to handle:
 * 1. offset/bank
 * 2. company/holding
 * 3. normal/reinvoicing
 */
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Getter
@Setter
@Entity
public class CategoryToAccount extends BaseEntity implements CompanyOwned {

    @ManyToOne(optional = false)
    private Company company;

    private String currency;

    @Enumerated(EnumType.STRING)
    private BankTransactionCategory category;

    // This is the most important, and first, "offset" account
    @Column(nullable = false)
    private String ledgerAccount;
    private String bank;
    private String ledgerHolding;
    private String bankHolding;
    private String ledgerReinvoicing;
    private String bankReinvoicing;
    private String ledgerReinvoicingHolding;
    private String bankReinvoicingHolding;

    private String comment;
}
