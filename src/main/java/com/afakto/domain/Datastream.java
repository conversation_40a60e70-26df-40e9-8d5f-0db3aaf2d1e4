package com.afakto.domain;

import java.util.HashSet;
import java.util.Set;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.afakto.domain.enumeration.DatastreamType;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * A Datastream.
 */
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Getter
@Setter
public class Datastream extends BaseEntity implements OrgIdOwned {
    @Column(nullable = false, updatable = false)
    private String orgId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DatastreamType type;

    @Column(nullable = false)
    private Boolean outgoing;

    @Column(nullable = false)
    private String path;

    @Column(nullable = false)
    @EqualsAndHashCode.Include
    private String name;

    private String log;

    private int inserts;
    private int updates;
    private int deletes;

    public void incrementInserts() {
        this.inserts++;
    }

    public void incrementUpdates() {
        this.updates++;
    }

    public void incrementDeletes() {
        this.deletes++;
    }

    private String error;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "datastream", orphanRemoval = true)
    @OrderBy("line")
    private Set<DatastreamFailure> failures = new HashSet<>();

    private int failuresCount;

    @PrePersist
    @PreUpdate
    public void updateFailuresCount() {
        failuresCount = failures.size();
    }
}
