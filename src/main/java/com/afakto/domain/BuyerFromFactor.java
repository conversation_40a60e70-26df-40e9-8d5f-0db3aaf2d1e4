package com.afakto.domain;

import java.math.BigDecimal;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.envers.Audited;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

@Audited
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Entity
@Getter
@Setter
public class BuyerFromFactor extends BaseEntity {
    @OneToOne(optional = false)
    private Buyer buyer;

    @Column(nullable = false)
    private String number;

    @Column(nullable = false)
    private String name;
    @Column(nullable = false)
    private String code;
    @Column(nullable = false)
    private String factorCode;
    @Column(nullable = false)
    private String currency;

    private BigDecimal amountApproved;

    @Column(nullable = false)
    private BigDecimal amountOutstanding;
    @Column(nullable = false)
    private BigDecimal amountDraftReceived;
    @Column(nullable = false)
    private BigDecimal amountFunded;
    @Column(nullable = false)
    private BigDecimal amountSecured;

    @Column(nullable = true)
    private String zipcode;
    @Column(nullable = true)
    private String city;

    private transient BigDecimal amountUnSecured;
    private transient BigDecimal amountUnFunded;
    private transient int decimals;

    public BuyerFromFactor calculateDecimals() {
        if (amountApproved != null)
            amountApproved = amountApproved.movePointLeft(decimals);
        amountOutstanding = amountOutstanding.movePointLeft(decimals);
        amountDraftReceived = amountDraftReceived.movePointLeft(decimals);
        amountFunded = amountOutstanding.subtract(amountUnFunded.movePointLeft(decimals));
        amountSecured = amountOutstanding.subtract(amountUnSecured.movePointLeft(decimals));

        decimals = 0;

        return this;
    }
}
