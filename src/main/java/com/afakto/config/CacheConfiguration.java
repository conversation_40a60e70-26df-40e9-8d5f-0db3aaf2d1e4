package com.afakto.config;

import java.time.Duration;

import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.jsr107.Eh107Configuration;
import org.hibernate.cache.jcache.ConfigSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.JCacheManagerCustomizer;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.boot.info.GitProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.afakto.domain.Address;
import com.afakto.domain.Authority;
import com.afakto.domain.BankTransaction;
import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromFactor;
import com.afakto.domain.BuyerFromInsurer;
import com.afakto.domain.CategoryToAccount;
import com.afakto.domain.Cession;
import com.afakto.domain.Company;
import com.afakto.domain.Contact;
import com.afakto.domain.Contract;
import com.afakto.domain.CreditLimit;
import com.afakto.domain.CreditLimitRequest;
import com.afakto.domain.Datastream;
import com.afakto.domain.FactorInstitution;
import com.afakto.domain.Gap;
import com.afakto.domain.Invoice;
import com.afakto.domain.InvoiceFromFactor;
import com.afakto.domain.Organization;
import com.afakto.domain.PaymentTerms;
import com.afakto.domain.User;

import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.config.cache.PrefixedKeyGenerator;

@Configuration
@EnableCaching
public class CacheConfiguration {

    private GitProperties gitProperties;
    private BuildProperties buildProperties;
    private final javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration;

    public CacheConfiguration(JHipsterProperties jHipsterProperties) {
        JHipsterProperties.Cache.Ehcache ehcache = jHipsterProperties.getCache().getEhcache();

        jcacheConfiguration = Eh107Configuration.fromEhcacheCacheConfiguration(
                CacheConfigurationBuilder.newCacheConfigurationBuilder(
                        Object.class,
                        Object.class,
                        ResourcePoolsBuilder.heap(ehcache.getMaxEntries()))
                        .withExpiry(ExpiryPolicyBuilder
                                .timeToLiveExpiration(Duration.ofSeconds(ehcache.getTimeToLiveSeconds())))
                        .build());
    }

    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer(javax.cache.CacheManager cacheManager) {
        return hibernateProperties -> hibernateProperties.put(ConfigSettings.CACHE_MANAGER, cacheManager);
    }

    @Bean
    public JCacheManagerCustomizer cacheManagerCustomizer() {
        return cm -> {
            createCache(cm, com.afakto.repository.UserRepository.USERS_BY_LOGIN_CACHE);
            createCache(cm, Address.class.getName());
            createCache(cm, Authority.class.getName());
            createCache(cm, BankTransaction.class.getName());
            createCache(cm, Buyer.class.getName());
            createCache(cm, Buyer.class.getName() + ".creditLimitHistories");
            createCache(cm, Buyer.class.getName() + ".creditLimitRequests");
            createCache(cm, Buyer.class.getName() + ".invoices");
            createCache(cm, BuyerFromFactor.class.getName());
            createCache(cm, BuyerFromInsurer.class.getName());
            createCache(cm, CategoryToAccount.class.getName());
            createCache(cm, Cession.class.getName());
            createCache(cm, Company.class.getName());
            createCache(cm, Contact.class.getName());
            createCache(cm, Contract.class.getName());
            createCache(cm, CreditLimit.class.getName());
            createCache(cm, CreditLimitRequest.class.getName());
            createCache(cm, FactorInstitution.class.getName());
            createCache(cm, Datastream.class.getName());
            createCache(cm, Gap.class.getName());
            createCache(cm, Invoice.class.getName());
            createCache(cm, InvoiceFromFactor.class.getName());
            createCache(cm, Organization.class.getName());
            createCache(cm, PaymentTerms.class.getName());
            createCache(cm, User.class.getName());
            createCache(cm, User.class.getName() + ".authorities");
            // LegalEntityController methods
            createCache(cm, "legalEntitySirenCache"); // Add the "legalEntitySirenCache"
            createCache(cm, "legalEntityNameCache"); // Add the "legalEntityNameCache"
            createCache(cm, "organizations"); // Add the "legalEntityNameCache"
            // jhipster-needle-ehcache-add-entry
        };
    }

    private void createCache(javax.cache.CacheManager cm, String cacheName) {
        javax.cache.Cache<Object, Object> cache = cm.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        } else {
            cm.createCache(cacheName, jcacheConfiguration);
        }
    }

    @Autowired(required = false)
    public void setGitProperties(GitProperties gitProperties) {
        this.gitProperties = gitProperties;
    }

    @Autowired(required = false)
    public void setBuildProperties(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @Bean
    public KeyGenerator keyGenerator() {
        return new PrefixedKeyGenerator(this.gitProperties, this.buildProperties);
    }
}
