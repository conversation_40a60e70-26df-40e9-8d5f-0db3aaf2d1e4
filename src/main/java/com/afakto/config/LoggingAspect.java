package com.afakto.config;

import java.util.Arrays;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Aspect for logging execution of service and repository Spring components.
 */
@Aspect
@Component
public class LoggingAspect {

    /**
     * Pointcut that matches all repositories, services and Web REST endpoints.
     */
    @Pointcut("within(@org.springframework.stereotype.Repository *)" +
            " || within(@org.springframework.stereotype.Service *)" +
            " || within(@org.springframework.web.bind.annotation.RestController *)")
    public void springBeanPointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the
        // advices.
    }

    /**
     * Pointcut that matches all Spring beans in the application's main packages.
     */
    @Pointcut("within(com.afakto.repository..*)" +
            " || within(com.afakto.service..*)" +
            " || within(com.afakto.web.rest..*)")
    public void applicationPackagePointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the
        // advices.
    }

    /**
     * Retrieves the {@link Logger} associated to the given {@link JoinPoint}.
     *
     * @param joinPoint join point we want the logger for.
     * @return {@link Logger} associated to the given {@link JoinPoint}.
     */
    private Logger logger(JoinPoint joinPoint) {
        return LoggerFactory.getLogger(joinPoint.getSignature().getDeclaringTypeName());
    }

    /**
     * Determines if an exception has already been handled by ExceptionTranslator
     * or other controller advices.
     *
     * @param e the exception to check
     * @return true if the exception is handled elsewhere
     */
    private boolean isHandledException(Throwable e) {
        // Exceptions handled by ExceptionTranslator
        return e instanceof org.springframework.security.access.AccessDeniedException ||
                e instanceof org.springframework.security.authentication.BadCredentialsException ||
                e instanceof org.springframework.security.authentication.InsufficientAuthenticationException ||
                e instanceof org.springframework.dao.ConcurrencyFailureException ||
                e instanceof java.util.NoSuchElementException ||
                e instanceof org.springframework.web.bind.MethodArgumentNotValidException ||
                e instanceof org.springframework.http.converter.HttpMessageConversionException ||
                e instanceof org.springframework.dao.DataAccessException ||
                e instanceof org.springframework.web.ErrorResponseException ||
                e instanceof com.afakto.web.rest.errors.BadRequestAlertException ||

                // Exceptions handled by custom controller advices
                e instanceof com.afakto.service.errors.BuyerNotFoundException ||
                e instanceof com.afakto.service.errors.BuyerBadRequestException ||
                e instanceof com.afakto.service.errors.ServerException ||

                // Spring MVC exceptions (handled by ResponseEntityExceptionHandler)
                e instanceof org.springframework.web.HttpMediaTypeNotSupportedException ||
                e instanceof org.springframework.web.HttpRequestMethodNotSupportedException ||
                e instanceof org.springframework.web.HttpMediaTypeNotAcceptableException ||
                e instanceof org.springframework.web.bind.MissingPathVariableException ||
                e instanceof org.springframework.web.bind.MissingServletRequestParameterException ||
                e instanceof org.springframework.web.bind.ServletRequestBindingException ||
                e instanceof org.springframework.web.multipart.support.MissingServletRequestPartException ||

                // Add any other exceptions that have @ResponseStatus annotation
                e.getClass().isAnnotationPresent(org.springframework.web.bind.annotation.ResponseStatus.class);
    }

    /**
     * Advice that logs methods throwing exceptions.
     *
     * @param joinPoint join point for advice.
     * @param e         exception.
     */
    @AfterThrowing(pointcut = "applicationPackagePointcut() && springBeanPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {
        // Skip logging if the exception is marked as handled
        if (isHandledException(e)) {
            return;
        }

        logger(joinPoint)
                .error(
                        "Exception in {}() with cause = '{}' and exception = '{}'",
                        joinPoint.getSignature().getName(),
                        e.getCause() != null ? e.getCause() : "NULL",
                        e.getMessage(),
                        e);
    }

    /**
     * Advice that logs when a method is entered and exited.
     *
     * @param joinPoint join point for advice.
     * @return result.
     * @throws Throwable throws {@link IllegalArgumentException}.
     */
    @Around("applicationPackagePointcut() && springBeanPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Logger log = logger(joinPoint);
        if (log.isDebugEnabled()) {
            log.debug("Enter: {}() with argument[s] = {}", joinPoint.getSignature().getName(),
                    Arrays.toString(joinPoint.getArgs()));
        }
        try {
            Object result = joinPoint.proceed();
            if (log.isDebugEnabled()) {
                // log.debug("Exit: {}() with result = {}", joinPoint.getSignature().getName(),
                // result);
                log.debug("Exit: {}()", joinPoint.getSignature().getName());
            }
            return result;
        } catch (IllegalArgumentException e) {
            log.error("Illegal argument: {} in {}()",
                    Arrays.toString(joinPoint.getArgs()),
                    joinPoint.getSignature().getName());
            throw e;
        }
    }
}
