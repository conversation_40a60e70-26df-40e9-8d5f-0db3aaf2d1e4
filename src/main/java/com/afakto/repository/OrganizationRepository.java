package com.afakto.repository;

import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.afakto.domain.Organization;

/**
 * Spring Data SQL repository for the Organization entity.
 */
@Repository
public interface OrganizationRepository extends JpaRepository<Organization, UUID> {
    Page<Organization> findAll(Pageable pageable);

    @Query("select o.name from Organization o where o.orgId = :orgId")
    String getOrgName(String orgId);

    Optional<Organization> findByNameIgnoreCase(String name);
}
