package com.afakto.repository;

import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.afakto.domain.Company;

/**
 * Spring Data SQL repository for the Company entity.
 */
@Repository
public interface CompanyRepository extends JpaRepository<Company, UUID> {
    Page<Company> findAll(Specification<Company> specification, Pageable pageable);

    @Query(value = """
            SELECT count(*) > 0
              FROM jhi_user_companies uc
              JOIN jhi_user u ON u.id = uc.user_id
             WHERE u.login = :login
               AND uc.companies_id = :companyId
                """, nativeQuery = true)
    boolean existsByUserLoginAndCompanyId(@Param("login") String login, @Param("companyId") UUID companyId);

    long count(Specification<Company> specification);

    Optional<Company> findOneByOrgIdAndCodeIgnoreCase(String orgId, String code);

    Optional<Company> findOneByCodeIgnoreCase(String code);

    Page<Company> findByNameStartsWithIgnoreCase(String name, Pageable pageable);

    // TODO quite a hack, to get an organisation name using some fragile conditions
    @Query("""
            SELECT LOWER(name)
              FROM Company c
              LEFT JOIN Contract ct ON ct.company = c
             WHERE c.orgId = ?1
               AND LOWER(c.name) = LOWER(c.code)
               AND ct.id IS NULL
            """)
    Optional<String> getOrgName(String orgId);

    @Modifying
    @Query(value = "DELETE FROM jhi_user_companies WHERE companies_id = :id", nativeQuery = true)
    void deleteFromAllPerimeters(UUID id);
}
