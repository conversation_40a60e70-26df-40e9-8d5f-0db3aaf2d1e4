package com.afakto.repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.afakto.domain.Company;
import com.afakto.domain.User;

/**
 * Spring Data JPA repository for the {@link User} entity.
 */
@Repository
public interface UserRepository
        extends JpaRepository<User, UUID>, JpaSpecificationExecutor<User> {
    String USERS_BY_LOGIN_CACHE = "usersByLogin";

    @EntityGraph(attributePaths = { "authorities", "companies", "companies.address", "companies.address.country",
            "preferences" })
    @Cacheable(cacheNames = USERS_BY_LOGIN_CACHE)
    Optional<User> findOneByLogin(String login);

    Page<User> findAllByIdNotNullAndActivatedIsTrue(Pageable pageable);

    Page<User> findAll(Specification<User> specification, Pageable pageable);

    List<User> findAllByOrgId(String orgId);

    List<User> findAllByCompanies(Company company);

    List<User> findAllByAuthorities(String authority);

    boolean existsByLoginIgnoreCase(String login);
}
