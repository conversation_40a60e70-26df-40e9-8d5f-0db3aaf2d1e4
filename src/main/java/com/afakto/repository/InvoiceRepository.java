package com.afakto.repository;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.afakto.domain.Buyer;
import com.afakto.domain.Cession;
import com.afakto.domain.Company;
import com.afakto.domain.Invoice;
import com.afakto.domain.enumeration.InvoiceType;

import jakarta.transaction.Transactional;

/**
 * Spring Data SQL repository for the Invoice entity.
 */
@Repository
public interface InvoiceRepository
        extends JpaRepository<Invoice, UUID>, JpaSpecificationExecutor<Invoice> {
    Optional<Invoice> findOneByBuyerAndTypeAndInvoiceNumber(
            Buyer buyer, InvoiceType type, String invoiceNumber);

    @Query("""
            SELECT i
              FROM Invoice i
             WHERE i.buyer.company = :company
               AND i.buyer.code = :buyerCode
               AND i.invoiceNumber = :invoiceNumber
               AND SIGN(i.amount) = SIGN(:amount)
            """)
    Optional<Invoice> findOneByBuyerCompanyAndBuyerCodeAndInvoiceNumberAndAmountSign(
            Company company, String buyerCode, String invoiceNumber, BigDecimal amount);

    @Query("""
            SELECT i
              FROM Invoice i
             WHERE i.buyer.company = :company
               AND i.invoiceNumber = :invoiceNumber
               AND SIGN(i.amount) = SIGN(:amount)
            """)
    Optional<Invoice> findOneByBuyerCompanyAndInvoiceNumberAndAmountSign(
            Company company, String invoiceNumber, BigDecimal amount);

    @EntityGraph(attributePaths = { "buyer",
            "buyer.paymentTerms",
            "buyer.buyerFromFactor",
            "invoiceFromFactor" })
    Page<Invoice> findAll(Specification<Invoice> specification, Pageable pageable);

    // Used in spring batch export job
    Page<Invoice> findAllByCessions(Cession cession, Pageable pageable);

    // Used in spring batch export job
    // to list all invoices which are only associated to this one cession
    @Query("""
            SELECT i
              FROM Invoice i
              JOIN i.cessions c
             WHERE i.type IN ('CREDIT_NOTE', 'INVOICE')
               AND c = :cession
               AND SIZE(i.cessions) = 1
            """)
    Page<Invoice> findAllByCessionsToRemit(Cession cession, Pageable pageable);

    @Modifying
    @Query("""
            UPDATE Invoice i SET balance = 0, lastModifiedBy = 'system', lastModifiedDate = CURRENT_TIMESTAMP, version = version + 1
             WHERE balance <> 0
               AND i.buyer.company.orgId = :orgId
               AND (i.buyer.company.code = :code OR 'all' = :code)
               AND i.id NOT IN :importedIds
            """)
    @Transactional
    int resetAllBalancesByOrgIdAndCompanyCode(String orgId, String code, Set<UUID> importedIds);

    Collection<Invoice> findAllByBuyerCompanyAndCurrencyAndIsUnderFactorIsTrue(
            Company company,
            String currency);

    Collection<Invoice> findAllByBuyerCompanyAndCurrencyAndIsUnderFactorIsFalseAndReconciliationJournalAndGapIsNull(
            Company company,
            String currency,
            String reconciliationJournal);

    @Query("""
            SELECT COALESCE(SUM(i.balance), 0.0)
              FROM Invoice i
              JOIN i.cessions c
             WHERE c = :cession
               AND i.type IN :types
            """)
    BigDecimal sumByCessionAndType(Cession cession, Set<InvoiceType> types);
}
