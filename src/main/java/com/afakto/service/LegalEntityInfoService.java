package com.afakto.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.afakto.domain.enumeration.NumberType;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.tasks.BuyerEnrichmentResult;
import feign.FeignException;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.afakto.service.dto.AddressDTO;
import com.afakto.service.feign.FeignInseeService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * This handles the calls made to the InseeService and handles
 * everything
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LegalEntityInfoService {
    private static final String DEFAULT_COUNTRY_CODE = "fr";

    private final FeignInseeService feignInseeService;

    /**
     *
     * @param buyer the buyer
     * @return return a Response with all the information
     */
    public BuyerEnrichmentResult getLegalEntityInfoByName(BuyerDTO buyer) {
        log.info("Getting legal entity for name: {}", buyer.getName().trim());
        try {
            Map<String, Object> enterprise = feignInseeService.getLegalEntityByName(buyer.getName().trim());
            if (enterprise == null)
                return BuyerEnrichmentResult.failure("SIREN_NAME_NOT_FOUND");

            return BuyerEnrichmentResult.success(updateBuyer(enterprise, buyer));
        } catch (FeignException e) {
            if (e.status() == 404)
                return BuyerEnrichmentResult.failure("SIREN_NAME_NOT_FOUND");
            throw new RuntimeException(e.getMessage());
        }
    }

    public BuyerEnrichmentResult getLegalEntityInfoByNumber(BuyerDTO buyer) {
        var number = buyer.getNumber();

        if (ObjectUtils.isEmpty(buyer.getNumber())) {
            return BuyerEnrichmentResult.failure("SIREN_INVALID");
        }

        number = number.trim().replaceAll("[-\\s]", "");
        if (!Character.isDigit(number.charAt(0))) {
            if (!number.startsWith("FR") || number.length() < 13)
                return null;

            number = number.substring(4, 13);
        }

        log.info("Getting legal entity for siren: {}", number);
        try {
            Map<String, Object> enterprise = feignInseeService.getLegalEntityBySiren(number);
            if (enterprise == null)
                return BuyerEnrichmentResult.failure("SIREN_NOT_FOUND");

            return BuyerEnrichmentResult.success(updateBuyer(enterprise, buyer));
        } catch (FeignException e) {
            if (e.status() == 404)
                return BuyerEnrichmentResult.failure("SIREN_NOT_FOUND");
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     *
     * @param enterprise this is the object we get from a request
     * @return a legal entity
     */
    private BuyerDTO updateBuyer(Map<String, Object> enterprise, BuyerDTO buyer) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> establishments = (List<Map<String, Object>>) enterprise.get("etablissements");
        if (establishments == null || establishments.isEmpty()) {
            // handle the case where establishments is null or empty
            return null;
        }

        Map<String, Object> buyerInfo = establishments.get(0);

        // log each buyerinfo value
        buyerInfo.forEach((key, value) -> log.info("BuyerInfo {}: {}", key, value));

        String sirenNumber = (String) buyerInfo.get("siren");
        buyer.setNumber(sirenNumber);
        buyer.setNumberType(NumberType.SIREN);

        @SuppressWarnings("unchecked")
        Map<String, String> map = (Map<String, String>) buyerInfo.get("uniteLegale");
        String legalName = map.get("denominationUniteLegale");
        buyer.setName(legalName);

        @SuppressWarnings("unchecked")
        Map<String, String> addressMap = (Map<String, String>) buyerInfo.get("adresseEtablissement");
        AddressDTO addressDTO = constructAddressDTO(addressMap);
        buyer.setAddress(addressDTO);

        return buyer;
    }

    private AddressDTO constructAddressDTO(Map<String, String> addressMap) {
        return new AddressDTO()
                .setStreetNumber(addressMap.getOrDefault("numeroVoieEtablissement", ""))
                .setStreetName(String.join(" ",
                        Optional.ofNullable(addressMap.get("typeVoieEtablissement")).orElse(""),
                        Optional.ofNullable(addressMap.get("libelleVoieEtablissement")).orElse("")))
                .setPostalCode(addressMap.getOrDefault("codePostalEtablissement", ""))
                .setCity(addressMap.getOrDefault("libelleCommuneEtablissement", ""))
                .setCountry(DEFAULT_COUNTRY_CODE);
    }
}
