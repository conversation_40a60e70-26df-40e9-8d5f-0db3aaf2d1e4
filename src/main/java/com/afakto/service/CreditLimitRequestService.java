package com.afakto.service;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.afakto.domain.Buyer;
import com.afakto.domain.CreditLimit;
import com.afakto.domain.CreditLimitRequest;
import com.afakto.domain.enumeration.CreditInsurance;
import com.afakto.domain.enumeration.CreditLimitRequestStatus;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.ContractRepository;
import com.afakto.repository.CreditLimitRequestRepository;
import com.afakto.service.dto.CreditLimitRequestDTO;
import com.afakto.service.insurer.InsurerCofaceService;
import com.afakto.service.mapper.CreditLimitRequestMapper;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service Implementation for managing {@link CreditLimitRequest}.
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class CreditLimitRequestService {
    private final BuyerRepository buyerRepository;
    private final ContractRepository contractRepository;
    private final CreditLimitRequestRepository creditLimitRequestRepository;
    private final CreditLimitRequestMapper creditLimitRequestMapper;
    private final EntityManager entityManager;
    private final InsurerCofaceService insurerCofaceService;

    /**
     * Save a creditLimitRequest.
     *
     * @param creditLimitRequestDTO the entity to save.
     * @return the persisted entity.
     */
    public CreditLimitRequestDTO save(CreditLimitRequestDTO creditLimitRequestDTO) {
        log.debug("Request to save CreditLimitRequest : {}", creditLimitRequestDTO);

        var creditLimitRequest = creditLimitRequestMapper.toEntity(creditLimitRequestDTO)
                .setBuyer(entityManager.getReference(
                        Buyer.class,
                        creditLimitRequestDTO.getBuyer().getId()))
                .setStatus(CreditLimitRequestStatus.REQUESTED);

        var contracts = contractRepository.findAllByCompanyAndFinancialInformationCurrencyAndActivationDateIsBefore(
                creditLimitRequest.getBuyer().getCompany(),
                creditLimitRequest.getCurrency(),
                LocalDate.now());

        var contract = contracts.stream()
                .findFirst()
                .orElse(null);

        if (contract == null)
            return null;

        if (contract.getCreditInsurance() == CreditInsurance.INTERNAL || contract.getCreditInsurancePolicy() == null) {
            creditLimitRequest
                .setAmount(creditLimitRequest.getRequestedAmount())
                .setStatus(CreditLimitRequestStatus.ACCEPTED);
            var buyer = creditLimitRequest.getBuyer();
            if (buyer.getCreditLimit() == null) {
                buyer.setCreditLimit(new CreditLimit());
            }
            buyer.getCreditLimit()
                    .setAmount(creditLimitRequest.getRequestedAmount())
                    .setCurrency(creditLimitRequest.getCurrency());

            buyer = buyerRepository.save(buyer);

            creditLimitRequest.setBuyer(buyer);

        } else if ("COFACE"
                .equalsIgnoreCase(contract.getCreditInsurancePolicy().getExternalCreditInsurance().getName())) {
            // We have an external policy, call it
            var request = insurerCofaceService.requestCreditLimit(
                    creditLimitRequest.getBuyer(),
                    creditLimitRequest.getRequestedAmount(),
                    creditLimitRequest.getCurrency());
            if (request == null)
                creditLimitRequest.setStatus(CreditLimitRequestStatus.ERROR);
            else
                creditLimitRequest
                        .setOrderCode(request.get("orderId").asText(null));
        }

        creditLimitRequest = creditLimitRequestRepository.save(creditLimitRequest);
        return creditLimitRequestMapper.toDto(creditLimitRequest);
    }

    public CreditLimitRequestDTO updateInsurerDecision(CreditLimitRequest creditLimitRequest) {
        log.info("Updating insurer decision for CreditLimitRequest: {}", creditLimitRequest);
        Buyer buyer = buyerRepository.findById(creditLimitRequest.getBuyer().getId()).orElseThrow();

        var insurerName = buyer.getBuyerFromInsurer().getInsurerName();

        switch (insurerName.toLowerCase()) {
            case "coface":
                var insurerDecision = insurerCofaceService.updateInsurerDecision(creditLimitRequest);
                creditLimitRequestRepository.save(insurerDecision);
                return creditLimitRequestMapper.toDto(insurerDecision);
            default:
                throw new IllegalArgumentException("Unknown insurer: " + insurerName);
        }
    }

    /**
     * Get all the creditLimitRequests.
     *
     * @return the list of entities.
     */
    @Transactional(readOnly = true)
    public Page<CreditLimitRequestDTO> findByBuyer(UUID buyerId, Pageable pageable) {
        log.debug("Request to get CreditLimitRequests by buyer id");
        return creditLimitRequestRepository.findAllByBuyerId(buyerId, pageable).map(creditLimitRequestMapper::toDto);
    }

    /**
     * Get all creditLimitRequests.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    @Transactional(readOnly = true)
    public Page<CreditLimitRequestDTO> findAll(Pageable pageable) {
        log.debug("Request to get all CreditLimitRequests");
        return creditLimitRequestRepository.findAll(pageable).map(creditLimitRequestMapper::toDto);
    }

    /**
     * Get one creditLimitRequest by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<CreditLimitRequestDTO> findOne(UUID id) {
        log.debug("Request to get CreditLimitRequest : {}", id);
        return creditLimitRequestRepository.findById(id).map(creditLimitRequestMapper::toDto);
    }
}
