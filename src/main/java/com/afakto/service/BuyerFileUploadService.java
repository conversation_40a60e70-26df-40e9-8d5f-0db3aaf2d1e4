package com.afakto.service;

import java.util.EnumMap;
import java.util.EnumSet;
import java.util.NoSuchElementException;

import com.afakto.domain.enumeration.DatastreamType;
import com.afakto.service.mapper.DatastreamMapper;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.Datastream;
import com.afakto.domain.PaymentTerms;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CompanyRepository;
import com.afakto.repository.DatastreamRepository;
import com.afakto.repository.UserRepository;
import com.afakto.security.SecurityUtils;
import com.afakto.service.dto.DatastreamDTO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class BuyerFileUploadService extends UploadService {
    private final BuyerRepository buyerRepository;
    private final CompanyRepository companyRepository;
    private final DatastreamMapper datastreamMapper;
    private final DatastreamRepository datastreamRepository;
    private final UserRepository userRepository;

    private static final DataFormatter dataFormatter = new DataFormatter();

    private enum Columns {
        COMPANY_CODE,
        BUYER_CODE,
        BUYER_NAME,
        ID_TYPE,
        ID_NUMBER,
        PAYMENT_TERMS
    }

    private EnumMap<Columns, Integer> headers = new EnumMap<>(Columns.class);

    protected BaseEntity processRecord(Row row) {
        if (row.getRowNum() == 0) {
            for (var cell : row) {
                try {
                    headers.put(Columns.valueOf(cell.toString()), cell.getColumnIndex());
                } catch (IllegalArgumentException e) {
                    log.info("Ignoring column: {}", cell);
                }
            }

            for (var column : Columns.values()) {
                if (!headers.containsKey(column)) {
                    log.warn("Missing column: {}", column);
                    throw new NoSuchElementException("Missing column: " + column);
                }
            }
            return null;

        }

        if (headers.isEmpty())
            return null;

        // The payment terms is not required
        if (row.getPhysicalNumberOfCells() < Columns.values().length - 1) {
            throw new NoSuchElementException("Missing column(s)");
        }

        var companyCode = dataFormatter.formatCellValue(row.getCell(headers.get(Columns.COMPANY_CODE)));
        var buyerCode = dataFormatter.formatCellValue(row.getCell(headers.get(Columns.BUYER_CODE)));
        var buyerName = dataFormatter.formatCellValue(row.getCell(headers.get(Columns.BUYER_NAME)));
        var numberType = NumberType
                .fromValue(row.getCell(headers.get(Columns.ID_TYPE)).getStringCellValue())
                .orElseThrow(() -> new NoSuchElementException(
                        "Buyer type not in " + EnumSet.allOf(NumberType.class)));
        var idNumber = dataFormatter.formatCellValue(row.getCell(headers.get(Columns.ID_NUMBER)));
        PaymentTerms paymentTerms = new PaymentTerms();
        try {
            paymentTerms.setNumberOfDays(
                    (int) row.getCell(headers.get(Columns.PAYMENT_TERMS))
                            .getNumericCellValue());
        } catch (Exception _ignored) {
        }

        Buyer buyer = buyerRepository.findOneByCompanyCodeAndCode(companyCode, buyerCode)
                .orElseGet(() -> new Buyer()
                        .setCode(buyerCode)
                        .setCompany(companyRepository.findOneByCodeIgnoreCase(companyCode).orElseThrow()))
                .setName(buyerName)
                .setNumberType(numberType)
                .setNumber(idNumber)
                .setPaymentTerms(paymentTerms);

        return buyerRepository.save(buyer);
    }

    /**
     * This method is for processing, each record
     *
     * @param csvRecord a row in a CSV file
     */
    protected BaseEntity processRecord(CSVRecord csvRecord) {
        for (var column : Columns.values()) {
            if (column == Columns.PAYMENT_TERMS)
                continue;
            if (!csvRecord.isSet(column.toString()) || csvRecord.get(column).isEmpty()) {
                throw new NoSuchElementException("Missing column: " + column);
            }
        }

        var companyCode = csvRecord.get(Columns.COMPANY_CODE);
        var buyerCode = csvRecord.get(Columns.BUYER_CODE);
        var buyerName = csvRecord.get(Columns.BUYER_NAME);
        NumberType numberType = NumberType
                .fromValue(csvRecord.get(Columns.ID_TYPE))
                .orElseThrow(() -> new NoSuchElementException(
                        "Buyer type not in " + EnumSet.allOf(NumberType.class)));
        String idNumber = csvRecord.get(Columns.ID_NUMBER);
        PaymentTerms paymentTerms = new PaymentTerms();
        try {
            paymentTerms.setNumberOfDays(Integer.valueOf(csvRecord.get(Columns.PAYMENT_TERMS)));
        } catch (Exception _ignored) {
        }

        Buyer buyer = buyerRepository.findOneByCompanyCodeAndCode(companyCode, buyerCode)
                .orElseGet(() -> new Buyer()
                        .setCode(buyerCode)
                        .setCompany(companyRepository.findOneByCodeIgnoreCase(companyCode).orElseThrow()))
                .setName(buyerName)
                .setNumberType(numberType)
                .setNumber(idNumber)
                .setPaymentTerms(paymentTerms);

        return buyerRepository.save(buyer);
    }

    @Override
    protected DatastreamDTO save(Datastream datastream) {
        datastream.setType(DatastreamType.BUYER);
        if (datastream.getOrgId() == null) {
            datastream.setOrgId(
                    userRepository.findOneByLogin(SecurityUtils.getCurrentUserLogin().orElseThrow())
                            .orElseThrow()
                            .getOrgId());
        }

        log.info("Failures: {}", datastream.getFailures().size());
        return datastreamMapper.toDto(datastreamRepository.save(datastream));
    }
}
