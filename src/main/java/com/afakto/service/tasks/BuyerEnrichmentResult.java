package com.afakto.service.tasks;

import com.afakto.service.dto.BuyerDTO;
import lombok.Getter;

@Getter
public class BuyerEnrichmentResult {
    private BuyerDTO buyer;
    private String errorCode;

    public static BuyerEnrichmentResult success(BuyerDTO buyer) {
        BuyerEnrichmentResult result = new BuyerEnrichmentResult();
        result.buyer = buyer;
        return result;
    }

    public static BuyerEnrichmentResult failure(String errorCode) {
        BuyerEnrichmentResult result = new BuyerEnrichmentResult();
        result.errorCode = errorCode;
        return result;
    }

    public boolean isSuccess() {
        return buyer != null;
    }

}
