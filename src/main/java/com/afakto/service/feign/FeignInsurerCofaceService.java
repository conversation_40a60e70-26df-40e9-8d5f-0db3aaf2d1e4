package com.afakto.service.feign;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.databind.JsonNode;

@FeignClient(name = "feign-insurer-coface-api", url = "${application.insurer.coface.base-url}")
public interface FeignInsurerCofaceService {
    String CREDIT_LIMIT_PRODUCT = "CREDITLIMIT";

    @PostMapping(value = "authentication/v1/token")
    Map<String, String> token(@RequestHeader("x-api-key") String apiKey, @RequestBody Map<String, String> body);

    @GetMapping(value = "tci/product/v2/companies/products?searchType=NORMAL")
    JsonNode getProducts(
            @RequestHeader("x-api-key") String apiKey,
            @RequestHeader("Authorization") String token,
            @RequestHeader("X-COF-Policy-Number") String policyNumber);

    @GetMapping(value = "tci/product/v2/companies/products?searchType=NORMAL&customerReferences={customerReferences}")
    JsonNode getProductsForBuyer(
            @RequestHeader("x-api-key") String apiKey,
            @RequestHeader("Authorization") String token,
            @RequestHeader("X-COF-Policy-Number") String policyNumber,
            @PathVariable("customerReferences") String customerReferences);

    @GetMapping(value = "tci/product/v2/companies/products?searchType=NORMAL&easyNumbers={easyNumbers}")
    JsonNode getProductsForEasyNumber(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("Authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber,
        @PathVariable("easyNumbers") String easyNumbers);

    @GetMapping(value = "tci/product/v2/companies/{easyNumber}/products/deliveries/{deliveryId}")
    JsonNode getDeliveryDecisionForEasyNumber(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("Authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber,
        @PathVariable("easyNumber") String easyNumber,
        @PathVariable("deliveryId") String deliveryId
        );

    @GetMapping(value = "tci/company/v2/companies/identifier?countryCode={countryCode}&identifierType={identifierType}&identifierValue={identifierValue}")
    JsonNode getBuyerFromIdentifier(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber,
        @PathVariable("countryCode") String countryCode,
        @PathVariable("identifierType") String identifierType,
        @PathVariable("identifierValue") String identifierValue);

    @GetMapping(value = "tci/company/v2/companies/name?countryCode={countryCode}&companyName={companyName}")
    JsonNode getBuyerFromCompanyName(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber,
        @PathVariable("countryCode") String countryCode,
        @PathVariable("companyName") String companyName);

    @GetMapping(value = "tci/country/v2/countries/identifiers")
    JsonNode getAllIdentifiers(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber);

    @PatchMapping(value = "tci/company/v2/companies/customerreferences")
    JsonNode patchCustomerReference(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber,
        @RequestBody Map<String, Object> body);

    @PostMapping(value = "tci/product/v2/companies/{easyNumber}/products",  consumes = { "application/json", "application/merge-patch+json" })
    JsonNode requestCreditLimitUpdate(
        @RequestHeader("x-api-key") String apiKey,
        @RequestHeader("authorization") String token,
        @RequestHeader("X-COF-Policy-Number") String policyNumber,
        @PathVariable("easyNumber") String easyNumber,
        @RequestBody Map<String, Object> body);
}
