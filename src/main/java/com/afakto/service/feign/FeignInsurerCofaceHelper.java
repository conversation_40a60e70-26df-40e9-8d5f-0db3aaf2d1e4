package com.afakto.service.feign;

import com.afakto.domain.Buyer;
import com.afakto.domain.Contract;
import com.afakto.repository.ContractRepository;
import com.afakto.repository.OrganizationRepository;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.mapper.BuyerMapper;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Service
@Slf4j
public class FeignInsurerCofaceHelper {
    private final BuyerMapper buyerMapper;
    private final ContractRepository contractRepository;
    private final Environment env;
    private final FeignInsurerCofaceService feignInsurerService;
    private final OrganizationRepository organizationRepository;

    private static final String INSURER_API_KEY = "ORG_%s_INSURER_COFACE_API_KEY";

    private final Cache<String, String> tokenCache = Caffeine.newBuilder()
        .expireAfterWrite(1, TimeUnit.DAYS)
        .build();

    // This is to manage "duplicate" data coming from the insurer.
    // Happens when insurer possesses multiple buyers with the same code.
    private boolean isTokenExpired(String token) {
        DecodedJWT decodedJWT = JWT.decode(token);
        return decodedJWT.getExpiresAt().before(new Date());
    }

    private static final String INSURER_LOGIN = "ORG_%s_INSURER_COFACE_LOGIN";
    private static final String INSURER_PASSWORD = "ORG_%s_INSURER_COFACE_PASSWORD";

    public FeignInsurerCofaceHeader from(Buyer buyer) {
        var company = buyer.getCompany();
        var orgName = organizationRepository.getOrgName(company.getOrgId());
        var apiKey = env.getProperty(String.format(INSURER_API_KEY, orgName));
        var idToken = getInsurerToken(orgName);
        var policyId = contractRepository.getPolicyId(company, buyer.getCurrency());
        return new FeignInsurerCofaceHeader(idToken, apiKey, policyId);
    }

    public FeignInsurerCofaceHeader from(BuyerDTO buyer) {
        return from(buyerMapper.toEntity(buyer));
    }

    public FeignInsurerCofaceHeader from(Contract contract) {
        var orgName = organizationRepository.getOrgName(contract.getCompany().getOrgId());
        var apiKey = env.getProperty(String.format(INSURER_API_KEY, orgName));
        var idToken = getInsurerToken(orgName);
        var policyId = contract.getCreditInsurancePolicy().getPolicyId();
        return new FeignInsurerCofaceHeader(idToken, apiKey, policyId);
    }


    public String getInsurerToken(String orgName) {
        String cachedToken = tokenCache.getIfPresent(orgName);
        if (cachedToken != null && !isTokenExpired(cachedToken)) {
            return cachedToken;
        }

        var apiKey = env.getProperty(String.format(INSURER_API_KEY, orgName));
        var login = env.getProperty(String.format(INSURER_LOGIN, orgName));
        var password = env.getProperty(String.format(INSURER_PASSWORD, orgName));
        if (ObjectUtils.isEmpty(apiKey) || ObjectUtils.isEmpty(login) || ObjectUtils.isEmpty(password)) {
            log.error("Insurer credentials are not set: {}, {}, {}",
                String.format(INSURER_API_KEY, orgName),
                String.format(INSURER_LOGIN, orgName),
                String.format(INSURER_PASSWORD, orgName));
            throw new IllegalStateException("Insurer credentials are not set");
        }

        String idToken = feignInsurerService.token(
                apiKey,
                Map.of("username", login, "password", password, "grantType", "password"))
            .get("idToken");

        tokenCache.put(orgName, idToken);
        return idToken;
    }
}
