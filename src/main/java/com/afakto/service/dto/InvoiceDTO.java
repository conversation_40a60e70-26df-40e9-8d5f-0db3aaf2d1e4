package com.afakto.service.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.domain.enumeration.PaymentMethod;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * A DTO for the {@link com.afakto.domain.Invoice} entity.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InvoiceDTO extends BaseDTO {

    @NotNull
    private BuyerDTO buyer;

    // Required to manage the @OneToOne non owning relation
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private InvoiceFromFactorDTO invoiceFromFactor;

    private InvoiceType type;
    private String invoiceNumber;
    private LocalDate date;
    private LocalDate dueDate;
    @NotNull
    private String currency;
    private BigDecimal amount;
    private BigDecimal balance;
    private PaymentMethod paymentMethod;
    @NotNull
    private boolean underFactor;
    private String reconciliationJournal;
    private LocalDate paymentDate;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private GapDTO gap;
}
