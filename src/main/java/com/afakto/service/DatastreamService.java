package com.afakto.service;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.afakto.domain.Datastream;
import com.afakto.service.dto.DatastreamDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.afakto.batch.ImportJob;
import com.afakto.repository.CompanyRepository;
import com.afakto.repository.DatastreamRepository;
import com.afakto.service.mapper.DatastreamMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service Implementation for managing {@link Datastream}.
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class DatastreamService {

    private final CompanyRepository companyRepository;
    private final DatastreamMapper datastreamMapper;
    private final DatastreamRepository datastreamRepository;
    private final ImportJob importJob;

    private static final String[] DIRS = { "archive", "error", "temp" };

    @Value("file:${application.filesystem}")
    private Resource dir;

    public Path reconstructPath(String top, DatastreamDTO datastream) {
        try {
            return dir
                    .getFile()
                    .toPath()
                    .resolve(top)
                    .resolve(datastream.getOrgId())
                    .resolve(datastream.getType().toString().toLowerCase())
                    .resolve(datastream.getPath())
                    .resolve(datastream.getName());
        } catch (IOException e) {
            log.error("Error reconstructing path", e);
        }
        return null;
    }

    /**
     * Save a datastream.
     *
     * @param datastreamDTO the entity to save.
     * @return the persisted entity.
     */
    public DatastreamDTO save(DatastreamDTO datastreamDTO) {
        log.debug("Request to save Datastream : {}", datastreamDTO);
        Datastream datastream = datastreamMapper.toEntity(datastreamDTO);
        datastream = datastreamRepository.save(datastream);
        return datastreamMapper.toDto(datastream);
    }

    /**
     * Update a datastream.
     *
     * @param datastreamDTO the entity to save.
     * @return the persisted entity.
     */
    public DatastreamDTO update(DatastreamDTO datastreamDTO) {
        log.debug("Request to update Datastream : {}", datastreamDTO);
        Datastream datastream = datastreamMapper.toEntity(datastreamDTO);
        datastream = datastreamRepository.save(datastream);
        return datastreamMapper.toDto(datastream);
    }

    /**
     * Partially update a datastream.
     *
     * @param datastreamDTO the entity to update partially.
     * @return the persisted entity.
     */
    public Optional<DatastreamDTO> partialUpdate(DatastreamDTO datastreamDTO) {
        log.debug("Request to partially update Datastream : {}", datastreamDTO);

        return datastreamRepository
                .findById(datastreamDTO.getId())
                .map(existingDatastream -> {
                    datastreamMapper.partialUpdate(existingDatastream, datastreamDTO);

                    return existingDatastream;
                })
                .map(datastreamRepository::save)
                .map(datastreamMapper::toDto);
    }

    /**
     * Get one datastream by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<DatastreamDTO> findOne(UUID id) {
        log.debug("Request to get Datastream : {}", id);
        return datastreamRepository.findById(id).map(datastreamMapper::toDto);
    }

    /**
     * Delete the datastream by id.
     *
     * @param id the id of the entity.
     */
    public void delete(UUID id) {
        log.debug("Request to delete Datastream : {}", id);
        datastreamRepository.deleteById(id);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DatastreamDTO upload(DatastreamDTO datastreamDTO, MultipartFile file) throws IOException {
        datastreamDTO.setName(file.getOriginalFilename());

        // Save file in the `temp` directory of the toExport path
        var path = reconstructPath("temp", datastreamDTO);
        path.toFile().getParentFile().mkdirs();

        // Create a temporary file to store the transformed content
        Path tempFile = Files.createTempFile("upload", ".tmp");

        // Read the file line by line, convert line endings, and write to the temporary
        // file
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8));
                BufferedWriter writer = new BufferedWriter(
                        new OutputStreamWriter(Files.newOutputStream(tempFile), StandardCharsets.UTF_8))) {
            String line;
            boolean isFirstLine = true;
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    line = removeBOM(line);
                    isFirstLine = false;
                }
                line = line.replace("\r\n", "\n").replace("\r", "\n");
                writer.write(line);
                writer.write('\n'); // Write Unix line ending
            }
        }

        // Move the temporary file to the final destination
        Files.move(tempFile, path, StandardCopyOption.REPLACE_EXISTING);

        return datastreamMapper.toDto(importJob.importPath(path));
    }

    private String removeBOM(String line) {
        if (line.startsWith("\uFEFF")) {
            return line.substring(1);
        }
        return line;
    }

    public Resource download(UUID id) throws IOException {
        var datastream = datastreamRepository.findById(id).map(datastreamMapper::toDto).orElseThrow();

        // We look into the "normal" datastream directories, or into the org_name one
        return findResource(datastream)
                .or(() -> companyRepository.getOrgName(datastream.getOrgId())
                        .flatMap(orgName -> findResource(datastream.setOrgId(orgName))))
                .orElseThrow(() -> new IOException("File not found"));
    }

    private Optional<FileSystemResource> findResource(DatastreamDTO datastream) {
        return Stream.of(DIRS)
                .map(dir -> reconstructPath(dir, datastream))
                .flatMap(path -> Stream.of(path, path.resolveSibling(path.getFileName() + ".gz")))
                .filter(path -> path.toFile().exists())
                .map(FileSystemResource::new)
                .findFirst();
    }
}
