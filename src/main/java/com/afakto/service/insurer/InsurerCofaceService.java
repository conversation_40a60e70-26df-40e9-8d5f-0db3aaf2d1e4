package com.afakto.service.insurer;

import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromInsurer;
import com.afakto.domain.Contract;
import com.afakto.domain.CreditLimitRequest;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.repository.BuyerFromInsurerRepository;
import com.afakto.repository.BuyerRepository;
import com.afakto.service.dto.AddressDTO;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.insurer.coface.RequestCreditLimit;
import com.afakto.service.insurer.coface.UpdateCreditLimit;
import com.afakto.service.tasks.BuyerEnrichmentResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.afakto.batch.Utils.extractCountry;
import static com.afakto.batch.Utils.mapTo3LetterCode;
import static com.afakto.service.insurer.coface.IdentifierHelper.*;

/**
 * This service is used to import data from Insurer's COFACE API.
 * <p></p>
 * API documentation:
 * <a href="https://developers.coface.com/docs-technical?sw=CofaServe%20-%20API%20Product.yaml&swl=API%20CofaServe%20-%20Product#tag/Product">...</a>
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class InsurerCofaceService {
    private final BuyerRepository buyerRepository;
    private final BuyerFromInsurerRepository buyerFromInsurerRepository;
    private final FeignInsurerCofaceService feignInsurerService;

    private final RequestCreditLimit requestCreditLimit;
    private final UpdateCreditLimit updateCreditLimit;

    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;
    private final ObjectMapper objectMapper;

    public JsonNode requestCreditLimit(Buyer buyer, BigDecimal amount, String currency) {
        buyer.setCurrency(currency);
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        var buyerFromInsurer = buyer.getBuyerFromInsurer();
        var insurerCode = buyerFromInsurer != null ? buyerFromInsurer.getInsurerCode() : null;

        if (insurerCode == null)
            insurerCode = requestCreditLimit.getInsurerCode(header, buyer);

        var response = requestCreditLimit.sendRequestCreditLimit(buyer, header, insurerCode, amount);

        if (buyerFromInsurer == null)
            buyerFromInsurer = new BuyerFromInsurer();
        buyerFromInsurer.setInsurerName("coface");
        if (response == null)
            buyerFromInsurer.setInsurerCode(insurerCode);
        else
            buyerFromInsurer.setInsurerCode(response.get("easyNumber").asText(null));

        buyerFromInsurer = buyerFromInsurerRepository.save(buyerFromInsurer.setBuyer(buyer));
        buyerRepository.save(buyer.setBuyerFromInsurer(buyerFromInsurer));

        return response;
    }

    public int updateCreditLimits(Contract contract) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(contract);

        var covers = feignInsurerService.getProducts(header.apiKey(), header.idToken(), header.policyId())
                .get("companies");

        updateCreditLimit.alreadyImported = new HashMap<>();
        var toSave = StreamSupport.stream(covers.spliterator(), false)
                .map(cover -> {
                    var buyer = updateCreditLimit.findBuyer(contract.getCompany(), cover);
                    if (buyer == null)
                        return null;
                    return updateCreditLimit.setupBuyerFromInsurer(buyer, cover);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        updateCreditLimit.alreadyImported = null;
        log.warn("Saving {} buyers", toSave.size());
        return buyerFromInsurerRepository.saveAll(toSave).size();
    }

    public Buyer updateCreditLimit(Buyer buyer) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        var easyNumber = buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode();
        JsonNode cover = updateCreditLimit.getCover(header, easyNumber, buyer.getCode());

        if (cover == null)
            throw new IllegalArgumentException("Couldn't find cover for buyer");

        if (!buyer.getCode().equals(cover.get("customerReferenceValue").asText(null)))
            updateCreditLimit.patchCustomerReference(header, easyNumber, buyer.getCode());
        var toSave = updateCreditLimit.setupBuyerFromInsurer(buyer, cover);
        if (toSave == null)
            return buyer;
        return buyer.setBuyerFromInsurer(buyerFromInsurerRepository.save(toSave));
    }

    public CreditLimitRequest updateInsurerDecision(CreditLimitRequest creditLimitRequest) {
        log.info("InsurerDecision update");
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(creditLimitRequest.getBuyer());

        var easyNumber = creditLimitRequest.getBuyer().getBuyerFromInsurer()  == null ? null :creditLimitRequest.getBuyer().getBuyerFromInsurer().getInsurerCode();
        JsonNode insurerDecision = feignInsurerService.getDeliveryDecisionForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), easyNumber, creditLimitRequest.getOrderCode());

        if (insurerDecision == null)
            throw new IllegalArgumentException("Couldn't find insurer decision for credit limit code " + creditLimitRequest.getOrderCode());

        JsonNode creditLimitPeriods = insurerDecision.path("product").path("creditLimitPeriods");
        JsonNode firstPeriod = creditLimitPeriods.get(0);
        JsonNode reasonCodeNode = firstPeriod
            .path("creditPeriodCondition")
            .path("reasonCodes")
            .get(0)
            .path("code");

        var code = reasonCodeNode.asText(null);

        if (code == null)
            throw new IllegalArgumentException("Missing 'code' field");
        var insurerComments = firstPeriod.path("underwriterComments");
        if (insurerComments != null && insurerComments.has(0))
            creditLimitRequest.setInsurerComment(insurerComments.get(0).asText(null));
        creditLimitRequest.setInsurerDecision(code);
        return creditLimitRequest;
    }

    public JsonNode getBuyerFromIdentifier(String identifierType, String identifierNumber, FeignInsurerCofaceHeader header) {
        if (isVAT(identifierType)) {
            identifierNumber = normalizeVATIdentifier(identifierNumber);
        }

        String country = extractCountry(identifierType, identifierNumber);
        String countryCode = mapTo3LetterCode(country);
        String mappedIdentifierType = mapIdentifierType(identifierType, countryCode);
        String cleanedIdentifierNumber = cleanIdentifierNumber(identifierNumber, countryCode);

        return feignInsurerService.getBuyerFromIdentifier(
            header.apiKey(), header.idToken(), header.policyId(),
            countryCode, mappedIdentifierType, cleanedIdentifierNumber);
    }

    private Optional<String> getCofaceErrorCode(String message) {
        int startIndex = message.indexOf("[{");
        int endIndex = message.lastIndexOf("}]") + "}]".length();

        if (startIndex == -1 || endIndex <= startIndex)
            return Optional.empty();

        message = message.substring(startIndex, endIndex);
        try {
            JsonNode node = objectMapper.readTree(message).get(0);
            String code = node.path("code").asText(null);
            if (code == null)
                return Optional.empty();

            if (code.equals("Client403-SOR_BR230236"))
                return Optional.of("COUNTRY_NOT_ALLOWED");
            if (code.equals("Client412"))
                return Optional.of("VAT_INVALID");
            return Optional.of(message);
        } catch (JsonProcessingException e) {
            System.out.println("Couldn't parse error message: " + message);
            return Optional.empty();
        }
    }

    public List<BuyerDTO> searchBuyer(BuyerDTO buyer, Contract contract) {
        log.debug("COFACE credit insurance policy found");
        List<BuyerDTO> buyers = new ArrayList<>();
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(contract);

        JsonNode node = feignInsurerService.getBuyerFromCompanyName(header.apiKey(), header.idToken(), header.policyId(), mapTo3LetterCode(buyer.getAddress().getCountry()), buyer.getName());
        if (node == null || !node.has("companies") || node.get("companies").isEmpty())
            return buyers;

        for (JsonNode company : node.get("companies"))
            buyers.add(fillBuyerData(company, new BuyerDTO()));

        return buyers;
    }

    public BuyerEnrichmentResult enrichBuyer(BuyerDTO buyer, Contract contract) {
        log.debug("COFACE credit insurance policy found");
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(contract);

        JsonNode node = null;
        if (buyer.getBuyerFromInsurer() != null && buyer.getBuyerFromInsurer().getInsurerCode() != null)
            node = feignInsurerService.getProductsForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), buyer.getBuyerFromInsurer().getInsurerCode());
        else if (buyer.getNumber() != null && buyer.getNumberType() != null) {
            try {
                node = getBuyerFromIdentifier(buyer.getNumberType().toString(), buyer.getNumber(), header);
            } catch (FeignException e) {
                Optional<String> cofaceErrorCode = getCofaceErrorCode(e.getMessage());
                return BuyerEnrichmentResult.failure(cofaceErrorCode.orElse(e.getMessage()));
            }
        }

        if (node == null || !node.has("companies") || node.get("companies").isEmpty())
            return BuyerEnrichmentResult.failure("NOT_FOUND");

        return BuyerEnrichmentResult.success(fillBuyerData(node.get("companies").get(0), buyer));
    }

    private BuyerDTO fillBuyerData(JsonNode node, BuyerDTO buyer) {
        var companyDetails = node.get("headOffice");
        var name = companyDetails.get("name");
        var address = companyDetails.get("address");
        AddressDTO addressDTO = new AddressDTO()
            .setCity(address.get("city").asText())
            .setCountry(address.get("countryCode").asText()
                .substring(0, 2).toLowerCase())
            .setPostalCode(address.get("postalCode").asText())
            .setStreetName(address.has("streets") && address.get("streets").isArray() && !address.get("streets").isEmpty() ? address.get("streets").get(0).asText() : null);
        var mainIdentifier = node.get("mainIdentifier");
        if (!mainIdentifier.has("value")) {
            var secondaryIdentifier = node.get("secondaryIdentifiers");
            if (secondaryIdentifier != null && secondaryIdentifier.isArray() && !secondaryIdentifier.isEmpty()) {
                buyer.setNumber(secondaryIdentifier.get(0).get("value").asText())
                    .setNumberType(NumberType.NATIONAL_REGISTRATION_NUMBER);
            }
        }
        else
            buyer.setNumber(mainIdentifier.get("value").asText())
            .setNumberType(NumberType.NATIONAL_REGISTRATION_NUMBER);

        return buyer.setAddress(addressDTO).setName(name.asText());
    }
}
