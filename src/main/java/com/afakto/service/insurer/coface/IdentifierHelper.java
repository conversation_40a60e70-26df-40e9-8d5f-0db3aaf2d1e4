package com.afakto.service.insurer.coface;

public class IdentifierHelper {

    public static String normalizeVATIdentifier(String id) {
        if (id.startsWith("LU") && id.length() > 2)
            return "LU " + id.substring(2);
        return id;
    }

    public static String mapIdentifierType(String originalType, String countryCode) {
        if (!isVAT(originalType)) return originalType;

        return switch (countryCode) {
            case "PRT" -> "TAX_PRT";
            case "ESP" -> "NIF_ESP";
            default -> "VAT_" + countryCode;
        };
    }

    public static String cleanIdentifierNumber(String id, String countryCode) {
        return switch (countryCode) {
            case "POL", "ROU" -> id.length() > 2 ? id.substring(2) : id;
            default -> id;
        };
    }

    public static boolean isVAT(String identifierType) {
        return "VAT".equalsIgnoreCase(identifierType);
    }

}
