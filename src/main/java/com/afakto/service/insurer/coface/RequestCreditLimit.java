package com.afakto.service.insurer.coface;

import com.afakto.domain.Buyer;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.StreamSupport;

import static com.afakto.batch.Utils.mapTo3LetterCode;

@RequiredArgsConstructor
@Service
@Slf4j
public class RequestCreditLimit {

    private final FeignInsurerCofaceService feignInsurerService;

    private static final String UNKNOWN_EASYNUMBER = "unknown";

    private String getNumberType(Buyer buyer) {
        if (buyer.getNumberType() == NumberType.VAT && !buyer.getAddress().getCountry().equals("FR"))
            return "VAT_" + mapTo3LetterCode(buyer.getAddress().getCountry());
        return buyer.getNumberType().toString();
    }

    public String getInsurerCode(FeignInsurerCofaceHeader header, Buyer buyer) {
        var buyerFromIdentifier = feignInsurerService.getBuyerFromIdentifier(
            header.apiKey(), header.idToken(), header.policyId(),
            mapTo3LetterCode(buyer.getAddress().getCountry()),
            getNumberType(buyer), buyer.getNumber());
        if (buyerFromIdentifier == null)
            return null;
        if (buyerFromIdentifier.has("companies") && !buyerFromIdentifier.get("companies").isEmpty())
            return buyerFromIdentifier.get("companies").get(0).get("easyNumber").asText();
        return null;
    }

    private Map<String, Object> getDeliveryType(FeignInsurerCofaceHeader header, String insurerCode) {
        var products = feignInsurerService.getProductsForEasyNumber(header.apiKey(), header.idToken(), header.policyId(), insurerCode);
        if (products == null || products.isEmpty()) {
            log.warn("No products found for easy number: {}", insurerCode);
            return null;
        }
        if (!products.has("companies") || products.get("companies").isEmpty()) {
            log.warn("No companies found for easy number: {}", insurerCode);
            return null;
        }

        products = StreamSupport.stream(products.get("companies").get(0).get("products").spliterator(), false)
            .filter(item -> item.has("productCode"))
            .findFirst()
            .orElse(null);

        if (products == null || !products.has("deliveryId")) {
            log.warn("No deliveryId found for easy number: {}", insurerCode);
            return createRequestCreditLimit();
        }

        if (products.get("deliveryId").isNull()) {
            log.warn("DeliveryId is null for easy number: {}", insurerCode);
            return null;
        }

        if (FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT.equals(products.get("productCode").asText()))
            return updateRequestCreditLimit(products.get("deliveryId").asText());
        else
            return changeRequestCreditLimit(products.get("deliveryId").asText());
    }

    private Map<String, Object> updateRequestCreditLimit(String deliveryId) {
        return Map.of(
            "actionCode", "UPDT",
            "deliveryId", deliveryId
        );
    }

    private Map<String, Object> changeRequestCreditLimit(String deliveryId) {
        return Map.of(
            "actionCode", "CHANGE",
            "deliveryId", deliveryId
        );
    }

    private Map<String, Object> createRequestCreditLimit() {
        return Map.of(
            "actionCode", "CREA"
        );
    }

    public Map<String, Object> createUnknownBuyerCreditLimit(Buyer buyer) {
        return Map.of(
            "actionCode", "CREA",
            "unknownDebtor", Map.of(
                "companyName", buyer.getName(),
                "countryCode", mapTo3LetterCode(buyer.getAddress().getCountry()),
                "address", Map.of(
                    "street", buyer.getAddress().getStreetNumber() + buyer.getAddress().getStreetName(),
                    "postalCode", buyer.getAddress().getPostalCode(),
                    "city", buyer.getAddress().getCity()))
        );
    }

    public JsonNode sendRequestCreditLimit(Buyer buyer, FeignInsurerCofaceHeader header,
                                            String insurerCode, BigDecimal amount) {
        Map<String, Object> map;
        if (UNKNOWN_EASYNUMBER.equals(insurerCode))
            map = createUnknownBuyerCreditLimit(buyer);
        else
            map = getDeliveryType(header, insurerCode);

        if (map == null)
            return null;

        if (insurerCode == null)
            insurerCode = UNKNOWN_EASYNUMBER;

        var request = new HashMap<>(Map.of(
            "productCode", FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT,
            "orderDetails", Map.of(
                "amount", amount,
                "currency", buyer.getCurrency())));

        request.putAll(map);

        return feignInsurerService.requestCreditLimitUpdate(header.apiKey(), header.idToken(), header.policyId(), insurerCode, request);
    }


}
