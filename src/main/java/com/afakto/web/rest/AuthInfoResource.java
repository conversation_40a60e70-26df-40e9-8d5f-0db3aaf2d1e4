package com.afakto.web.rest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Resource to return information about OIDC properties
 */
@RequestMapping("/api")
@RestController
public class AuthInfoResource {
    @Value("${spring.security.oauth2.client.provider.oidc.issuer-uri:}")
    private String issuer;

    @Value("${spring.security.oauth2.client.registration.oidc.client-id:}")
    private String clientId;

    @GetMapping("auth-info")
    public AuthInfoVM getAuthInfo() {
        return new AuthInfoVM(issuer, clientId);
    }

    @Data
    @AllArgsConstructor
    class AuthInfoVM {
        private String issuer;
        private String clientId;
    }

    /**
     * Auth0 requires a trip to the application, then a redirect to the auth0
     * enrollment page
     */
    @GetMapping("login")
    public ResponseEntity<String> login(
            HttpServletRequest request,
            @RequestParam(required = false) String invitation,
            @RequestParam(required = false) String organization) {
        var location = issuer + "authorize?response_type=code&client_id=" + clientId
                + "&invitation=" + invitation
                + "&organization=" + organization
                + "&redirect_uri="
                + request.getRequestURL().toString().replace("/api/login", "");

        return ResponseEntity.status(HttpStatus.FOUND).header("Location", location).build();
    }
}
