package com.afakto.batch.sg;

import static com.afakto.domain.enumeration.DatastreamType.SG_DEBTOR;
import static com.afakto.domain.enumeration.NumberType.VAT;
import static java.util.Optional.ofNullable;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.UUID;

import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemReaderBuilder;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.batch.item.file.transform.FieldExtractor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;

import com.afakto.batch.Utils;
import com.afakto.domain.Address;
import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.Cession;
import com.afakto.domain.Contact;
import com.afakto.domain.CreditInsurancePolicy;
import com.afakto.domain.CreditLimit;
import com.afakto.domain.Datastream;
import com.afakto.domain.enumeration.DatastreamType;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.repository.BuyerRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class SgDebtorExportJob {
    @Value("file:${application.filesystem}/out/")
    private Resource toExport;

    private final BuyerRepository buyerRepository;

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter
            .ofPattern("yyyyMMdd")
            .withZone(ZoneId.systemDefault());

    private static final Map<String, Sort.Direction> READER_SORT = Map.of(
            "createdDate", Sort.Direction.ASC);

    public void process(Cession cession) throws JobExecutionAlreadyRunningException, JobRestartException,
            JobInstanceAlreadyCompleteException, JobParametersInvalidException, IOException {
        log.info("Exporting SG debtor file for contract {}", cession.getContract().getContractNumber());

        jobLauncher.run(
                new JobBuilder("exportSgDebtorJob", jobRepository)
                        .start(createStep(cession))
                        .build(),
                new JobParametersBuilder()
                        .addDate("date", new Date())
                        .addJobParameter("cession", cession.getId(), UUID.class)
                        .addJobParameter("type", SG_DEBTOR, DatastreamType.class)
                        .toJobParameters());

        log.info("File exported successfully");
    }

    Step createStep(Cession cession) throws IOException {
        var contract = cession.getContract();
        var datastream = new Datastream()
                .setOrgId(contract.getCompany().getOrgId())
                .setOutgoing(true)
                .setType(DatastreamType.SG_DEBTOR)
                .setPath("all")
                .setName("NA"
                        + contract.getFactorAccountNumber()
                        + LocalDate.now().toString().replace("-", "")
                        + ".txt");

        if (cession.getDatastreams() == null) {
            cession.setDatastreams(new HashSet<>());
        }
        cession.getDatastreams().add(datastream);

        return new StepBuilder("exportBnpBalanceStep", jobRepository)
                .<Buyer, Buyer>chunk(1000, transactionManager)
                .faultTolerant()
                .reader(new RepositoryItemReaderBuilder<Buyer>()
                        .name("sgDebtorReader")
                        .repository(buyerRepository)
                        .methodName("findAllWithoutFactor")
                        .arguments(
                                cession.getContract().getCompany(),
                                cession.getContract().getFinancialInformation().getCurrency())
                        .pageSize(1000)
                        // Required so that the export should not "double" some exports when paginating
                        .sorts(READER_SORT)
                        .build())
                .writer(new FlatFileItemWriterBuilder<Buyer>()
                        .name("sgDebtorWriter")
                        .resource(Utils.getResource(toExport, datastream))
                        .delimited()
                        .delimiter(";")
                        .fieldExtractor(createFieldExtractor(cession))
                        .shouldDeleteIfExists(true)
                        .build())
                .listener(new ItemWriteListener<BaseEntity>() {
                    @Override
                    public void afterWrite(Chunk<? extends BaseEntity> items) {
                        datastream.setInserts(items.getItems().size());
                    }
                })
                .build();
    }

    /**
     * Example from the documentation:
     *
     * <code>
     * ADEB;.;04;AAAAMMJJ;A;20061031;11;0001;0007247;001;D;EUR;410152979;
     * ADEB;.;04;AAAAMMJJ;A;20061031;11;0001;0007247;001;D;EUR;393549407;
     * ADEB;.;04;AAAAMMJJ;A;20061031;11;0001;0007247;001;D;EUR;306299066;
     * ADEB;.;04;AAAAMMJJ;A;20061031;11;0001;0007247;001;D;EUR;427280136;
     * </code>
     *
     * <pre>
    | No | Data Name                      | Type        | Mandatory | Remarks                                                                |
    |----|--------------------------------|-------------|-----------|------------------------------------------------------------------------|
    | 1  | File type                      | X(4)        | Yes       | Must be set to ‘ADEB’                                                  |
    | 2  | Decimal separator              | X(1)        | Yes       | Comma ‘,’ or full stop ‘.’                                             |
    | 3  | Version                        | X(2)        | Yes       | Must be set to ‘04’                                                    |
    | 4  | Date Format                    | X(10)       | Yes       | Default value ‘YYYYMMDD’                                               |
    | 5  | Type of Debtor account number  | X(1)        | Yes       | Must be set to ‘A’                                                     |
    | 6  | File creation Date             | X(10)       | Yes       | Format YYYYMMDD                                                        |
    | 7  | Factor Company code            | 9(2)        | Yes       | Must be set to ‘11’                                                    |
    | 8  | Client account factor number   | X(15)       | Yes       | Must be set to ‘00001’                                                 |
    | 9  | Client account number          | 9(7)        | Yes       | Your client number in SGF’s ledger                                     |
    | 10 | Client Agreement number        | 9(3)        | Yes       | Your Agreement number in SGF’s ledger                                  |
    | 11 | Activity code                  | X(1)        | Yes       | ‘E’ for Export, ‘D’ for Domestic                                       |
    | 12 | Client account currency code   | X(3)        | Yes       | ISO code (see appendix)                                                |
    | 13 | Debtor company government number | 9(9)      | Yes       | Mandatory for French debtor                                            |
    | 14 | Debtor company branch number   | 9(5)        | No        |                                                                        |
    | 15 | Agreement amount               | 9(13)       | Yes       | Without decimal                                                        |
    | 16 | Agreement currency             | X(3)        | Yes       | ISO code (see appendix)                                                |
    | 17 | Debtor’s Company Name          | X(40)       | Yes       |                                                                        |
    | 18 | Billing address line 1         | X(40)       | Yes       | At least one line must be filled                                       |
    | 19 | Billing address line 2         | X(40)       | No        |                                                                        |
    | 20 | Billing address line 3         | X(40)       | No        |                                                                        |
    | 21 | Zip code                       | X(12)       | Yes       |                                                                        |
    | 22 | Town (billing address)         | X(40)       | Yes       |                                                                        |
    | 23 | Country code (billing address) | X(3)        | Yes       | ISO code (see appendix)                                                |
    | 24 | Telephone number               | X(20)       | Yes       |                                                                        |
    | 25 | Debtor Account Number          | X(30)       | Yes       | Debtor number in your ledger (Only first 10 characters can be entered) |
    | 26 | Agreement Date                 | X(10)       | No        | YYYYMMDD                                                               |
    | 27 | Filler                         | 9(5)        | No        |                                                                        |
    | 28 | Filler                         | 9(5)        | No        |                                                                        |
    | 29 | Filler                         | X(11)       | No        |                                                                        |
    | 30 | Filler                         | 9(2)        | No        |                                                                        |
    | 31 | Filler                         | X(4)        | No        |                                                                        |
    | 32 | Filler                         | X(30)       | No        |                                                                        |
    | 33 | Filler                         | X(11)       | No        |                                                                        |
    | 34 | VAT ECC number                 | X(14)       | Yes       | Mandatory for EU debtor                                                |
    | 35 | Debtor’s country code          | X(3)        | Yes       | ISO code (see appendix)                                                |
    | 36 | Company Name (paying address)  | X(40)       | No        |                                                                        |
    | 37 | Paying address line 1          | X(40)       | No        |                                                                        |
    | 38 | Paying address line 2          | X(40)       | No        |                                                                        |
    | 39 | Paying address line 3          | X(40)       | No        |                                                                        |
    | 40 | Zip code (French postal code)  | X(5)        | No        |                                                                        |
    | 41 | Town (paying address)          | X(40)       | No        |                                                                        |
    | 42 | Country code (Paying address)  | X(3)        | No        | ISO code (see appendix)                                                |
    | 43 | Insurance policy number        | X(20)       | No        | Mandatory if insurance policy is delegated                             |
    | 44 | Credit Insurer Debtor account  | X(60)       | Yes       | Debtor account given by your credit insurer if delegated               |
    | 45 | Payment delays                 | 9(3)        | No        | Number of days                                                         |
    | 46 | Filler                         | X(1)        | No        | Space                                                                  |
    | 47 | Correspondent name             | X(30)       | No        |                                                                        |
    | 48 | Foreign debtor Identifier      | X(40)       | Yes       | Mandatory for debtor external to EU                                    |
    | 49 | Filler                         | +9(15)V99   | No        |                                                                        |
    | 50 | Filler                         | +9(15)V99   | No        |                                                                        |
    | 51 | Filler                         | +9(15)V99   | No        |                                                                        |
    | 52 | Filler                         | +9(15)V99   | No        |                                                                        |
    | 53 | Filler                         | X(100)      | No        |                                                                        |
    | 54 | Filler                         | X(100)      | No        |                                                                        |
    | 55 | Filler                         | X(100)      | No        |                                                                        |
    | 56 | Filler                         | X(100)      | No        |                                                                        |
     * </pre>
     */
    FieldExtractor<Buyer> createFieldExtractor(Cession cession) {
        return buyer -> new String[] {
                "ADEB", // 1: Record Type
                ".", // 2: Separator
                "04", // 3: Version
                "AAAAMMJJ", // 4: Date
                "A", // 5: Type
                dateTimeFormatter.format(Instant.now()), // 6: Creation Date
                "11", // 7: Code
                "00001", // 8: Sequence Number
                cession.getContract().getFactorAccountNumber(), // 9: Factor Account Number
                cession.getContract().getContractNumber(), // 10: Contract Number
                "D", // 11: Contract Type
                cession.getContract().getFinancialInformation().getCurrency(), // 12: Currency

                Utils.extractSiren(buyer), // 13: SIREN
                NumberType.SIRET.equals(buyer.getNumberType()) ? buyer.getNumber().substring(9) : "", // 14: NIC
                ofNullable(buyer.getCreditLimit())
                        .map(CreditLimit::getAmount)
                        .map(Utils::getAmountWithoutDecimal)
                        .orElseGet(() -> ofNullable(cession.getContract().getCreditInsurancePolicy())
                                .map(CreditInsurancePolicy::getBlindCoverAmount)
                                .map(Utils::getAmountWithoutDecimal)
                                .orElse("")), // 15: Credit Limit
                cession.getContract().getFinancialInformation().getCurrency(), // 16: Credit Limit Currency
                buyer.getName(), // 17: Buyer Name
                ofNullable(buyer.getAddress())
                        .map(address -> String.join(" ",
                                ofNullable(address.getStreetNumber()).orElse(""),
                                ofNullable(address.getStreetName()).orElse(".")))
                        .map(String::trim)
                        .orElse("."), // 18: Street Address
                "", // 19: Address Line 2
                "", // 20: Address Line 3
                ofNullable(buyer.getAddress()).map(Address::getPostalCode).orElse("."), // 21: Postal Code
                ofNullable(buyer.getAddress()).map(Address::getCity).orElse("."), // 22: City
                extractCountryCode(buyer), // 23: Country Code
                ofNullable(buyer.getContact()).map(Contact::getPhone).orElse(""), // 24: Phone Number
                buyer.getCode(), // 25: Buyer Code
                dateTimeFormatter.format(cession.getContract().getActivationDate()), // 26: Activation Date
                "", // 27: Reserved
                "", // 28: Reserved
                "", // 29: Reserved
                "", // 30: Reserved
                "", // 31: Reserved
                "", // 32: Reserved
                "", // 33: Reserved
                VAT == buyer.getNumberType() ? buyer.getNumber() : "", // 34: VAT Number
                extractCountryCode(buyer), // 35: Address Country Code
                "", // 36: Company Name (Paying address)
                "", // 37: Paying address line 1
                "", // 38: Paying address line 2
                "", // 39: Paying address line 3
                "", // 40: Zip code (French postal code)
                "", // 41: Town (Paying address)
                "", // 42: Country code (Paying address)
                "", // 43: Insurance policy number
                "", // 44: Credit Insurer Debtor account
                "", // 45: Payment delays
                "", // 46: Foreign debtor Identifier
                "", // 47: Correspondent name
                "", // 48: Foreign debtor Identifier
                "", // 49: Filler
                "", // 50: Filler
                "", // 51: Filler
                "", // 52: Filler
                "", // 53: Filler
                "", // 54: Filler
                "", // 55: Filler
                "", // 56: Filler
        };
    }

    private String extractCountryCode(Buyer buyer) {
        if (VAT == buyer.getNumberType() && buyer.getNumber().length() >= 2) {
            return Utils.mapTo3LetterCode(buyer.getNumber().substring(0, 2));
        }

        Address address = buyer.getAddress();
        if (address == null || ObjectUtils.isEmpty(address.getCountry())) {
            return "";
        }

        return Utils.mapTo3LetterCode(address.getCountry());
    }
}
