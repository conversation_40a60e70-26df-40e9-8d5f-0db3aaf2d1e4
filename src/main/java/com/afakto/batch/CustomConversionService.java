package com.afakto.batch;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Locale;
import java.util.Optional;

import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.core.convert.support.GenericConversionService;
import org.springframework.stereotype.Service;

import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.domain.enumeration.PaymentMethod;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class CustomConversionService {
    private final GenericConversionService conversionService = new DefaultConversionService();

    // Define a DateTimeFormatter to handle the accepted formats
    private static final DateTimeFormatter DATE_FORMATTER = new DateTimeFormatterBuilder()
            .appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS"))
            .appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSS"))
            .appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            .appendOptional(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
            .appendOptional(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
            .toFormatter();

    // 57802,75 French
    // 57,802.75 English
    private BigDecimal convertToBigDecimal(String value) {
        if (value == null || value.isEmpty())
            return null;

        if (value.contains("."))
            return parseNumber(value, Locale.ENGLISH)
                    .or(() -> parseNumber(value, Locale.FRENCH))
                    .map(number -> new BigDecimal(number.toString()))
                    .orElseThrow(() -> new IllegalArgumentException("Invalid number format: " + value));

        return parseNumber(value, Locale.FRENCH) // Take away eventual spaces
                .or(() -> parseNumber(value, Locale.ENGLISH))
                .map(number -> new BigDecimal(number.toString()))
                .orElseThrow(() -> new IllegalArgumentException("Invalid number format: " + value));
    }

    // Exemple value: 1 812,59
    private Optional<Number> parseNumber(String value, Locale locale) {
        NumberFormat format = NumberFormat.getInstance(locale);
        String cleanedValue = value.replace("\u00A0", "").replace(" ", ""); // Remove non-breaking and regular spaces
        try {
            return Optional.of(format.parse(cleanedValue));
        } catch (ParseException e) {
            return Optional.empty();
        }
    }

    private InvoiceType convertToInvoiceType(String value) {
        return InvoiceType.fromValue(value).orElseThrow();
    }

    private LocalDate convertToLocalDate(String value) {
        if (value == null || value.isEmpty())
            return null;

        return LocalDate.parse(value, DATE_FORMATTER);
    }

    private NumberType convertToNumberType(String value) {
        return NumberType.fromValue(value).orElseThrow();
    }

    private PaymentMethod convertToPaymentMethod(String value) {
        return PaymentMethod.fromValue(value).orElseThrow();
    }

    @PostConstruct
    public void init() {
        conversionService.addConverter(String.class, BigDecimal.class, this::convertToBigDecimal); // Add this line
        conversionService.addConverter(String.class, InvoiceType.class, this::convertToInvoiceType);
        conversionService.addConverter(String.class, LocalDate.class, this::convertToLocalDate);
        conversionService.addConverter(String.class, NumberType.class, this::convertToNumberType);
        conversionService.addConverter(String.class, PaymentMethod.class, this::convertToPaymentMethod);
    }

    public ConversionService getConversionService() {
        return conversionService;
    }
}
