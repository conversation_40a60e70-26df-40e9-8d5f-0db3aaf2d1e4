package com.afakto.batch;

import static java.util.Map.entry;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.batch.core.ItemProcessListener;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.data.builder.RepositoryItemWriterBuilder;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.builder.FlatFileItemReaderBuilder;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.BindException;

import com.afakto.domain.Address;
import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.Datastream;
import com.afakto.domain.DatastreamFailure;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CompanyRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class BuyerImport {
    private final BuyerRepository buyerRepository;
    private final CompanyRepository companyRepository;
    private final CustomConversionService customConversionService;

    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private DelimitedLineTokenizer lineTokenizer;

    private static final Map<String, String> HEADER_TO_FIELD_MAPPING = Map.ofEntries(
            entry("ADDRESS_CITY", "address.city"),
            entry("ADDRESS_COUNTRY", "address.country"),
            entry("ADDRESS_POSTAL_CODE", "address.postalCode"),
            entry("ADDRESS_STATE", "address.stateProvince"),
            entry("ADDRESS_STREET_NAME", "address.streetName"),
            entry("ADDRESS_STREET_NUMBER", "address.streetNumber"),
            entry("BUYER_CODE", "code"),
            entry("BUYER_NAME", "name"),
            entry("COMPANY", "company.code"),
            entry("COMPANY_CODE", "company.code"),
            entry("CONTACT_EMAIL", "contact.email"),
            entry("CONTACT_NAME", "contact.name"),
            entry("CONTACT_PHONE", "contact.phone"),
            entry("ID_NUMBER", "number"),
            entry("ID_TYPE", "number_type"),
            entry("PAYMENT_TERMS", "paymentTerms.numberOfDays"));

    public Step createStep(Datastream datastream, Resource resource) {
        var lineNumber = new AtomicInteger(1);

        return new StepBuilder("importBuyerStep", jobRepository)
                .<Buyer, Buyer>chunk(1, transactionManager)
                .reader(new FlatFileItemReaderBuilder<Buyer>()
                        .beanMapperStrict(false)
                        .lineMapper(createLineMapper())
                        .linesToSkip(1)
                        .skippedLinesCallback(this::setupHeader)
                        .name("buyerReader")
                        .resource(resource)
                        .strict(false)
                        .build())
                .processor(line -> processLine(datastream, lineNumber.get(), line))
                .writer(new RepositoryItemWriterBuilder<Buyer>()
                        .repository(buyerRepository)
                        .build())
                .faultTolerant()
                .skip(FlatFileParseException.class)
                .skip(NoSuchElementException.class)
                .skipLimit(1000)
                .listener(new ItemReadListener<Buyer>() {
                    @Override
                    public void beforeRead() {
                        lineNumber.incrementAndGet();
                    }

                    @Override
                    public void onReadError(Exception e) {
                        log.warn("Trouble reading line {}", lineNumber);

                        var fff = new DatastreamFailure().setDatastream(datastream);
                        datastream.getFailures().add(fff);
                        if (e instanceof FlatFileParseException ffp) {
                            fff
                                    .setLine(ffp.getLineNumber())
                                    .setMessage(ffp.getCause().getMessage())
                                    .setRaw(ffp.getInput());
                            if (ffp.getCause() instanceof BindException be) {
                                var error = be.getFieldError();
                                fff.setMessage(
                                        "Error on field: " + error.getField()
                                                + ". Rejected value: "
                                                + ObjectUtils.nullSafeToString(error.getRejectedValue()));
                            }
                        } else {
                            fff
                                    .setLine(lineNumber.get())
                                    .setMessage(e.getCause().getMessage())
                                    .setRaw(e.getMessage());
                        }
                    }
                })
                .listener(new ItemProcessListener<BaseEntity, BaseEntity>() {
                    @Override
                    public void afterProcess(BaseEntity before, BaseEntity after) {
                        if (after == null)
                            return;
                        if (after.getId() == null)
                            datastream.incrementInserts();
                        else
                            datastream.incrementUpdates();
                    }

                    @Override
                    public void onProcessError(BaseEntity entity, Exception e) {
                        log.warn("Trouble processing line {}, exception: {}", lineNumber, e.getMessage());
                        datastream.getFailures().add(
                                new DatastreamFailure()
                                        .setDatastream(datastream)
                                        .setLine(lineNumber.get())
                                        .setMessage(e.getMessage()));
                    }
                })
                .build();
    }

    private LineMapper<Buyer> createLineMapper() {
        lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setStrict(false);

        BeanWrapperFieldSetMapper<Buyer> fieldSetMapper = new BeanWrapperFieldSetMapper<>();
        fieldSetMapper.setConversionService(customConversionService.getConversionService());
        fieldSetMapper.setStrict(false);
        fieldSetMapper.setTargetType(Buyer.class);

        DefaultLineMapper<Buyer> defaultLineMapper = new DefaultLineMapper<>();
        defaultLineMapper.setFieldSetMapper(fieldSetMapper);
        defaultLineMapper.setLineTokenizer(lineTokenizer);

        return defaultLineMapper;
    }

    /**
     * Process the skipped line to map the header to the field name.
     *
     * @param headerLine the header line
     */
    private void setupHeader(String headerLine) {
        // Determine and set the delimiter
        String delimiter = headerLine.contains(";") ? ";" : ",";
        lineTokenizer.setDelimiter(delimiter);

        lineTokenizer.setNames(
                Arrays.stream(headerLine.split(delimiter))
                        .map(String::trim)
                        .map(String::toUpperCase)
                        .map(header -> header.replace(' ', '_'))
                        .map(header -> HEADER_TO_FIELD_MAPPING.getOrDefault(header, header))
                        .toArray(String[]::new));
    }

    private void mapAddress(Address source, Address target) {
        if (source == null) {
            return;
        }

        for (Field field : Address.class.getDeclaredFields()) {
            field.setAccessible(true); // Allow access to private fields
            try {
                Object value = field.get(source);
                if (value instanceof String strValue && !strValue.isEmpty()) {
                    field.set(target, strValue);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to map field: " + field.getName(), e);
            }
        }
    }

    Buyer processLine(Datastream datastream, int lineNumber, Buyer line) {
        log.info("Processing line: {}", line.getCode());

        if (line.getCompany() == null)
            throw new NoSuchElementException("Company not present in the file");

        line.setCompany(companyRepository
                .findOneByOrgIdAndCodeIgnoreCase(datastream.getOrgId(), line.getCompany().getCode())
                .orElseThrow(() -> new NoSuchElementException("Company not found: " + line.getCompany().getCode())));

        var buyer = buyerRepository.findOneByCompanyAndCode(line.getCompany(), line.getCode())
                .orElseGet(() -> new Buyer()
                        .setCode(line.getCode())
                        .setCompany(line.getCompany()))
                .setName(line.getName())
                .setPaymentTerms(line.getPaymentTerms());

        if (buyer.getAddress() == null) {
            buyer.setAddress(line.getAddress());
        } else {
            mapAddress(line.getAddress(), buyer.getAddress());
        }

        if (buyer.getAddress() != null) {
            String country = buyer.getAddress().getCountry();
            if (country != null) {
                buyer.getAddress().setCountry(country.toLowerCase());
            }
        }

        if (buyer.getContact() == null) {
            buyer.setContact(line.getContact());
            // TODO map contact data
        }

        if (!ObjectUtils.isEmpty(line.getNumber()))
            // Remove all non-alphanumeric characters and convert to uppercase
            buyer.setNumber(line.getNumber().replaceAll("[^a-zA-Z0-9]", "").toUpperCase());
        if (!ObjectUtils.isEmpty(line.getNumberType()))
            buyer.setNumberType(line.getNumberType());

        if (buyer.getNumberType() == null || buyer.getNumber() == null) {
            datastream.getFailures().add(new DatastreamFailure()
                    .setDatastream(datastream)
                    .setLine(lineNumber)
                    .setMessage(
                            "ID number and type are required for buyer: " + line.getName()
                                    + " [code: " + line.getCode() + "]"));
            return null;
        }

        return buyer;
    }
}
