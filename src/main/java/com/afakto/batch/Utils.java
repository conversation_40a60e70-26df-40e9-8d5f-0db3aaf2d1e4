package com.afakto.batch;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Locale;
import java.util.MissingResourceException;

import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.util.ObjectUtils;

import com.afakto.domain.Buyer;
import com.afakto.domain.Contract;
import com.afakto.domain.Datastream;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Utils {

    public static String getExportOrDomestic(Contract contract, Buyer buyer) {
        if (buyer.getAddress() == null
                || ObjectUtils.isEmpty(buyer.getAddress().getCountry())
                || contract.getCountry().equals(buyer.getAddress().getCountry())) {
            return "D";
        }
        return "E";
    }

    public static String getAmountWithoutDecimal(BigDecimal amount) {
        if (amount == null) {
            return "0";
        }
        String amountStr = amount.toPlainString();
        int decimalIndex = amountStr.indexOf('.');
        return decimalIndex == -1 ? amountStr : amountStr.substring(0, decimalIndex);
    }

    public static String extractSiren(Buyer buyer) {
        String number = buyer.getNumber();
        if (ObjectUtils.isEmpty(number)) {
            return "";
        }

        switch (buyer.getNumberType()) {
            case SIREN:
                return number;
            case SIRET:
                return number.substring(0, 9);
            case VAT:
                String sanitizedNumber = number.replaceAll("[-\\s]", "");
                if (!sanitizedNumber.startsWith("FR") || sanitizedNumber.length() < 13)
                    return "";
                return sanitizedNumber.substring(4, 13);
            default:
                return "";
        }
    }

    public static String extractCountry(String identifierType, String identifierNumber) {
        switch (identifierType.toUpperCase()) {
            case "SIREN":
                return "FR";
            case "VAT":
                if (identifierNumber.length() < 2)
                    return "";
                return identifierNumber.substring(0, 2);
            default:
                return "";
        }
    }

    public static String mapTo3LetterCode(String countryCode) {
        String result = "";
        try {
            result = Locale.of("", countryCode).getISO3Country();
        } catch (MissingResourceException e) {
            log.warn("Trouble mapping country code to 3-letter code: " + countryCode);
        }
        return result;
    }

    // Helper method to format the amount with a sign
    public static String formatAmountWithSign(BigDecimal amount) {
        if (amount == null) {
            return "";
        }
        return (amount.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + amount.toPlainString();
    }

    public static FileSystemResource getResource(Resource toExport, Datastream datastream) throws IOException {
        String path = String.join("/",
                datastream.getOrgId(),
                datastream.getType().toString().toLowerCase(),
                datastream.getPath());

        Path resolvedPath = toExport.getFile().toPath().resolve(path);

        Files.createDirectories(resolvedPath);

        return new FileSystemResource(resolvedPath.resolve(datastream.getName()));
    }
}
