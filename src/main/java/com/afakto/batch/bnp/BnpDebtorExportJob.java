package com.afakto.batch.bnp;

import static com.afakto.domain.enumeration.DatastreamType.BNP_DEBTOR;
import static java.util.Optional.ofNullable;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.UUID;

import org.springframework.batch.core.ItemWriteListener;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.data.builder.RepositoryItemReaderBuilder;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.batch.item.file.transform.FieldExtractor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;

import com.afakto.batch.Utils;
import com.afakto.domain.Address;
import com.afakto.domain.BaseEntity;
import com.afakto.domain.Buyer;
import com.afakto.domain.Cession;
import com.afakto.domain.Contact;
import com.afakto.domain.CreditLimit;
import com.afakto.domain.Datastream;
import com.afakto.domain.enumeration.CreditInsurance;
import com.afakto.domain.enumeration.DatastreamType;
import com.afakto.domain.enumeration.NumberType;
import com.afakto.repository.BuyerRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class BnpDebtorExportJob {
    @Value("file:${application.filesystem}/out/")
    private Resource toExport;

    private final BuyerRepository buyerRepository;

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private static final Map<String, Sort.Direction> READER_SORT = Map.of(
            "createdDate", Sort.Direction.ASC);

    private static final String FILE_HEADER = String.join(";",
            "Code client", "Raison sociale", "Adresse 1", "Adresse 2", "Code postal", "Ville",
            "Interlocuteur", "Téléphone", "Fax", "Identifiant international", "Type d’identifiant",
            "NIC", "Montant de l’agrément", "Numéro du contrat", "Devise", "Zone réservée",
            "Code contrat", "Mode de paiement", "RIB", "Code pays");

    public void process(Cession cession) throws JobExecutionAlreadyRunningException, JobRestartException,
            JobInstanceAlreadyCompleteException, JobParametersInvalidException, IOException {
        log.info("Exporting BNP debtor file for contract {}", cession.getContract().getContractNumber());

        jobLauncher.run(
                new JobBuilder("exportBnpDebtorJob", jobRepository)
                        .start(createStep(cession))
                        .build(),
                new JobParametersBuilder()
                        .addDate("date", new Date())
                        .addJobParameter("cession", cession.getId(), UUID.class)
                        .addJobParameter("type", BNP_DEBTOR, DatastreamType.class)
                        .toJobParameters());

        log.info("File exported successfully");
    }

    Step createStep(Cession cession) throws IOException {
        var contract = cession.getContract();
        var datastream = new Datastream()
                .setOrgId(contract.getCompany().getOrgId())
                .setOutgoing(true)
                .setType(DatastreamType.BNP_DEBTOR)
                .setPath("all")
                .setName(LocalDate.now().toString().replace("-", "")
                        + "_bnp_debtor_"
                        + contract.getContractNumber()
                        + ".csv");

        if (cession.getDatastreams() == null) {
            cession.setDatastreams(new HashSet<>());
        }
        cession.getDatastreams().add(datastream);

        return new StepBuilder("exportBnpDebtorStep", jobRepository)
                .<Buyer, Buyer>chunk(1000, transactionManager)
                .faultTolerant()
                .reader(new RepositoryItemReaderBuilder<Buyer>()
                        .name("bnpDebtorReader")
                        .repository(buyerRepository)
                        .methodName("findAllWithoutFactor")
                        .arguments(
                                cession.getContract().getCompany(),
                                cession.getContract().getFinancialInformation().getCurrency())
                        .pageSize(1000)
                        // Required so that the export should not "double" some exports when paginating
                        .sorts(READER_SORT)
                        .build())
                .processor(buyer -> isBuyerValid(buyer) ? buyer : null)
                .writer(new FlatFileItemWriterBuilder<Buyer>()
                        .name("bnpDebtorWriter")
                        .resource(Utils.getResource(toExport, datastream))
                        .delimited()
                        .delimiter(";")
                        .fieldExtractor(createFieldExtractor(cession))
                        .headerCallback(writer -> writer.write(FILE_HEADER))
                        .shouldDeleteIfExists(true)
                        .build())
                .listener(new ItemWriteListener<BaseEntity>() {
                    @Override
                    public void afterWrite(Chunk<? extends BaseEntity> items) {
                        datastream.setInserts(items.getItems().size());
                    }
                })
                .build();
    }

    private boolean isBuyerValid(Buyer buyer) {
        return buyer != null
                && buyer.getCode() != null
                && buyer.getName() != null;
        // Add any other validation rules
    }

    /**
     * <pre>
    | Libellé                | Début | Fin  | Long. | Type* | Nature | Commentaires                                                                 |
    |------------------------|-------|------|-------|-------|--------|------------------------------------------------------------------------------|
    | Code client            | 1     | 35   | 35    | AN    | O      | Il s’agit du code que vous utilisez pour identifier vos clients.             |
    | Raison sociale         | 36    | 75   | 40    | AN    | O      | Nom de votre client                                                          |
    | Adresse 1              | 76    | 113  | 38    | AN    | O      |                                                                              |
    | Adresse 2              | 114   | 151  | 38    | AN    | F      |                                                                              |
    | Code postal            | 152   | 166  | 15    | AN    | O      |                                                                              |
    | Ville                  | 167   | 201  | 35    | AN    | O      |                                                                              |
    | Interlocuteur          | 202   | 221  | 20    | AN    | F      | Contact chez votre client (nom ou e-mail)                                    |
    | Téléphone              | 222   | 246  | 25    | AN    | F      | Téléphone du contact                                                         |
    | Fax                    | 247   | 271  | 25    | AN    | F      | Fax du contact                                                               |
    | Identifiant international | 272 | 291 | 20    | AN    | O      | Pour les acheteurs français : SIREN (9 chiffres)                             |
    | Type d’identifiant     | 292   | 311  | 20    | AN    | O      | Indiquer le mot « SIREN » ou « Code TVA » ou « Id_Coface »                   |
    | NIC                    | 312   | 316  | 5     | N     | F/O    | Obligatoire pour les établissements français (5 derniers chiffres du SIRET)  |
    | Montant de l’agrément  | 317   | 326  | 10    | N     | O/F    | Montant de l’agrément sollicité                                              |
    | Numéro du contrat      | 327   | 334  | 8     | N     | O      | Numéro de contrat attribué par BNP Paribas Factor « 00xxxxxx » ou « 01xxxxxx |
    | Devise                 | 335   | 337  | 3     | AN    | O      | Codification ISO (EUR ou autres)                                             |
    | Zone réservée          | 338   | 339  | 2     | O     |        | Laisser vide                                                                 |
    | Code Contrat           | 340   | 340  | 1     | AN    | F      | Code "C" : Contrat                                                           |
    | Mode de paiement       | 341   | 341  | 1     | AN    | F      | Voir table p.6                                                               |
    | RIB                    | 342   | 371  | 30    | AN    | F/O    | Obligatoire si le mode de paiement est « LCR non acceptée » (Code 9)         |
    |                        |       |      |       |       |        | Le RIB doit être saisi sans espace et caractère spécial                      |
    | Code pays              | 372   | 373  | 2     | AN    | O      | Code ISO du pays (FR, GB, DE, …)                                             |
     * </pre>
     */
    FieldExtractor<Buyer> createFieldExtractor(Cession cession) {
        return buyer -> new String[] {
                buyer.getCode(), // Code client
                buyer.getName(), // Raison sociale
                ofNullable(buyer.getAddress())
                        .map(address -> String.join(" ",
                                ofNullable(address.getStreetNumber()).orElse(""),
                                ofNullable(address.getStreetName()).orElse(".")))
                        .map(String::trim)
                        .orElse(""), // Adresse 1
                "", // Adresse 2
                ofNullable(buyer.getAddress()).map(Address::getPostalCode).orElse(""), // Code postal
                ofNullable(buyer.getAddress()).map(Address::getCity).orElse(""), // Ville
                ofNullable(buyer.getContact()).map(Contact::getName).orElse(""), // Interlocuteur
                ofNullable(buyer.getContact()).map(Contact::getPhone).orElse(""), // Téléphone
                "", // Fax
                extractIdentifier(buyer), // Identifiant international
                extractIdentifierType(buyer), // Type d’identifiant
                buyer.getNumberType() == NumberType.SIRET ? buyer.getNumber().substring(9) : "", // NIC
                extractCreditLimitAmount(cession, buyer.getCreditLimit()), // Montant de l’agrément
                cession.getContract().getContractNumber(), // Numéro du contrat
                cession.getContract().getFinancialInformation().getCurrency(), // Devise
                "", // Zone réservée
                "C",
                "", // Mode de paiement
                "", // RIB
                ofNullable(buyer.getAddress()).map(Address::getCountry).map(String::toUpperCase).orElse("") // Code pays
        };
    }

    private String extractIdentifier(Buyer buyer) {
        var siren = Utils.extractSiren(buyer);
        if (!ObjectUtils.isEmpty(siren))
            return siren;

        return buyer.getNumber();
    }

    private String extractIdentifierType(Buyer buyer) {
        switch (buyer.getNumberType()) {
            case COMMERCIAL_REGISTER:
                return "Reg_Comm";
            case DUNS_BRADSTREET:
                return "DUNS_Nb";
            case NATIONAL_REGISTRATION_NUMBER:
                return "Reg_Num";
            case SIREN, SIRET:
                return "Siren";
            case VAT:
                if (buyer.getNumber().startsWith("FR"))
                    return "Siren";
                return "Code_TVA";
            default:
                return "";
        }
    }

    /*
     * Attention: le montant doit être un multiple de 1000 et un minimum de 2000. Si
     * vous avez montant de 16 223,23
     * ous devez arrondir et indiquer dans le fichier 17 000.
     * Ce montant ne doit pas être renseigné (laisser vide) en cas d'assurance
     * externe ("délégation d'assurance")
     */
    private String extractCreditLimitAmount(Cession cession, CreditLimit creditLimit) {
        // Do not output anyting if credit insurance is not EXTERNAL
        if (CreditInsurance.EXTERNAL == cession.getContract().getCreditInsurance()) {
            return "";
        }
        if (creditLimit == null
                || creditLimit.getAmount() == null
                || creditLimit.getAmount().compareTo(BigDecimal.ZERO) == 0
                || creditLimit.getAmount().compareTo(new BigDecimal(2000)) < 0) {
            return "";
        }

        try {
            BigDecimal roundedAmount = creditLimit
                    .getAmount()
                    .divide(new BigDecimal(1000))
                    .setScale(0, RoundingMode.CEILING)
                    .multiply(new BigDecimal(1000));
            return Utils.getAmountWithoutDecimal(roundedAmount);
        } catch (ArithmeticException e) {
            log.error("Error calculating credit limit amount for contract {}: {}",
                    cession.getContract().getContractNumber(),
                    e.getMessage());
            return "";
        }
    }
}
