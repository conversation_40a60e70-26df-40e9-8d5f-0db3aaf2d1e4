package com.afakto.batch.bnp;

import static com.afakto.domain.enumeration.DatastreamType.BNP_BALANCE;
import static com.afakto.domain.enumeration.InvoiceType.CREDIT_NOTE;
import static com.afakto.domain.enumeration.InvoiceType.INVOICE;
import static com.afakto.domain.enumeration.InvoiceType.OTHER;
import static com.afakto.domain.enumeration.InvoiceType.UNALLOCATED_PAYMENT;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Currency;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.data.builder.RepositoryItemReaderBuilder;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import com.afakto.domain.Cession;
import com.afakto.domain.Contract;
import com.afakto.domain.Datastream;
import com.afakto.domain.Invoice;
import com.afakto.domain.enumeration.ActionType;
import com.afakto.domain.enumeration.DatastreamType;
import com.afakto.domain.enumeration.InvoiceType;
import com.afakto.domain.enumeration.PaymentMethod;
import com.afakto.repository.InvoiceRepository;
import com.afakto.service.FileService;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * For each assignment, this file contains the stock of all the outstanding
 * entries.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BnpBalanceExportJob {
    @Value("file:${application.filesystem}/out/")
    private Resource toExport;

    private final FileService fileService;
    private final InvoiceRepository invoiceRepository;

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager transactionManager;

    private static final Map<String, Sort.Direction> READER_SORT = new LinkedHashMap<>();
    static {
        READER_SORT.put("date", Sort.Direction.ASC);
        READER_SORT.put("invoiceNumber", Sort.Direction.ASC);
    }

    private static final Map<InvoiceType, String> ENTRY_TYPES = Map.of(
            CREDIT_NOTE, "C",
            INVOICE, "D",
            UNALLOCATED_PAYMENT, "C");

    private static final Map<InvoiceType, String> INVOICE_TYPES = Map.of(
            CREDIT_NOTE, "AV",
            INVOICE, "FA",
            OTHER, "OD",
            UNALLOCATED_PAYMENT, "PN");

    private static final Map<PaymentMethod, Integer> PAYMENT_CODES = Map.of(
            PaymentMethod.BANK_CHECK, 1,
            PaymentMethod.DRAFT, 2,
            PaymentMethod.BANK_TRANSFER, 4,
            PaymentMethod.OTHERS, 6,
            PaymentMethod.BAO, 7,
            PaymentMethod.STANDING_ORDER, 8);

    @RequiredArgsConstructor
    private static class JobContext {
        private final Cession cession;

        private int lineNumber;
    }

    @PostConstruct
    public void init() {
        fileService.init(toExport);
    }

    public void process(Cession cession) throws JobExecutionAlreadyRunningException, JobRestartException,
            JobInstanceAlreadyCompleteException, JobParametersInvalidException, IOException {
        log.info("Exporting BNP balance file for contract {}", cession.getContract().getContractNumber());

        JobContext context = new JobContext(cession); // New context per execution

        jobLauncher.run(
                new JobBuilder("exportBnpBalanceJob", jobRepository)
                        .start(createStep(context))
                        .build(),
                new JobParametersBuilder()
                        .addDate("date", new Date())
                        .addJobParameter("cession", cession.getId(), UUID.class)
                        .addJobParameter("type", BNP_BALANCE, DatastreamType.class)
                        .toJobParameters());

        log.info("File exported successfully");
    }

    Step createStep(JobContext context) throws IOException {
        var contract = context.cession.getContract();
        var datastream = new Datastream()
                .setOrgId(contract.getCompany().getOrgId())
                .setOutgoing(true)
                .setType(DatastreamType.BNP_BALANCE)
                .setPath("all")
                .setName(
                        LocalDate.now().toString().replace("-", "")
                                + "_bnp_balance_"
                                + contract.getContractNumber() + ".txt")
                .setInserts(context.cession.getMetadata().values().stream()
                        .map(metadata -> metadata.get(ActionType.INSERT))
                        .map(entry -> entry[0].intValue()).reduce(0, Integer::sum))
                .setUpdates(context.cession.getMetadata().values().stream()
                        .map(metadata -> metadata.get(ActionType.UPDATE))
                        .map(entry -> entry[0].intValue()).reduce(0, Integer::sum))
                .setDeletes(context.cession.getMetadata().values().stream()
                        .map(metadata -> metadata.get(ActionType.DELETE))
                        .map(entry -> entry[0].intValue()).reduce(0, Integer::sum));

        if (context.cession.getDatastreams() == null) {
            context.cession.setDatastreams(new HashSet<>());
        }
        context.cession.getDatastreams().add(datastream);

        return new StepBuilder("exportBnpBalanceStep", jobRepository)
                .<Invoice, Invoice>chunk(1000, transactionManager)
                .faultTolerant()
                .reader(new RepositoryItemReaderBuilder<Invoice>()
                        .name("bnpBalanceReader")
                        .repository(invoiceRepository)
                        .methodName("findAllByCessions")
                        .arguments(context.cession)
                        .pageSize(1000)
                        // Required so that the export should not "double" some exports when paginating
                        .sorts(READER_SORT)
                        .build())
                .writer(new FlatFileItemWriterBuilder<Invoice>()
                        .encoding("ISO-8859-1") // Set the encoding to ISO-8859-1
                        .name("bnpBalanceWriter")
                        .resource(getResource(datastream))
                        .shouldDeleteIfExists(true)
                        .headerCallback(writer -> writer.write(headerCallback(context)))
                        .lineAggregator(invoice -> lineAggregator(context, contract, invoice))
                        .footerCallback(writer -> writer.write(footerCallback(context, contract)))
                        .build())
                .build();
    }

    private String headerCallback(JobContext context) {
        context.lineNumber = 1;

        var contract = context.cession.getContract();
        var company = contract.getCompany();
        var currency = contract.getFinancialInformation().getCurrency();
        log.info("currency: {}", currency);

        var decimals = Currency.getInstance(contract.getFinancialInformation().getCurrency())
                .getDefaultFractionDigits();
        log.info("decimals: {}", decimals);

        var sumOutstanding = invoiceRepository.sumByCessionAndType(context.cession, Set.of(CREDIT_NOTE, INVOICE, OTHER))
                .movePointRight(decimals).longValue();
        log.info("sumOutstanding: {}", sumOutstanding);
        var sumUnallocatedPayment = invoiceRepository.sumByCessionAndType(context.cession, Set.of(UNALLOCATED_PAYMENT))
                .movePointRight(decimals).longValue();
        log.info("sumUnallocatedPayment: {}", sumUnallocatedPayment);
        var sumCreditNotes = invoiceRepository.sumByCessionAndType(context.cession, Set.of(CREDIT_NOTE))
                .movePointRight(decimals).longValue();
        log.info("sumCreditNotes: {}", sumCreditNotes);
        var sumOtherTransactions = invoiceRepository.sumByCessionAndType(context.cession, Set.of(OTHER))
                .movePointRight(decimals).longValue();
        log.info("sumOtherTransactions: {}", sumOtherTransactions);

        return String.format(
                "01%-8.8s%-30.30s%-30.30s%tY%<tm%<td%35s%017d%017d%017d%017d%017d%017d%1s%3s%1d%tY%<tm%<td%4s%62s%06d",
                contract.getContractNumber(),
                company.getName(),
                "BNP Paribas Factor",
                LocalDate.now(),
                "",
                Math.abs(sumOutstanding),
                0,
                Math.abs(sumUnallocatedPayment),
                Math.abs(sumOutstanding + sumUnallocatedPayment),
                Math.abs(sumCreditNotes),
                Math.abs(sumOtherTransactions),
                sumOtherTransactions >= 0 ? "P" : "N",
                currency,
                Currency.getInstance(currency).getDefaultFractionDigits(),
                LocalDate.now(),
                "V3.0",
                "",
                context.lineNumber++);
    }

    private String lineAggregator(JobContext context, Contract contract, Invoice invoice) {
        var decimals = Currency.getInstance(invoice.getCurrency()).getDefaultFractionDigits();
        return String.format(
                "55%-8.8s%-35.35s%-20.20s%-9.9s%-9.9s%-5.5s%-20.20s%-2.2s%-35.35s%tY%<tm%<td%tY%<tm%<td%-1s%017d%017d%1s%10s%50s%37s%06d",
                contract.getContractNumber(),
                invoice.getBuyer().getCode(),
                invoice.getBuyer().getName(),
                "",
                "",
                "",
                "",
                INVOICE_TYPES.get(invoice.getType()),
                invoice.getInvoiceNumber(),
                invoice.getDate(),
                invoice.getDueDate(),
                getEntryType(invoice),
                Math.abs(invoice.getBalance().movePointRight(decimals).longValue()),
                Math.abs(invoice.getAmount().movePointRight(decimals).longValue()),
                PAYMENT_CODES.get(Optional.ofNullable(invoice.getPaymentMethod()).orElse(PaymentMethod.BANK_TRANSFER)),
                "",
                "",
                "",
                context.lineNumber++);
    }

    private String footerCallback(JobContext context, Contract contract) {
        return String.format(
                "98%-8.8s%-30.30s%-30.30s%tY%<tm%<td%08d%-208.208s%06d%n",
                contract.getContractNumber(),
                contract.getCompany().getName(),
                "BNP Paribas Factor",
                LocalDate.now(),
                context.lineNumber - 2,
                "",
                context.lineNumber++) + "\n";
    }

    /**
     * C and D mean "Credit" or "Debit" respectively.
     *
     * Generally from type of the invoice, except of OTHER, which depends on the
     * balance sign
     */
    private String getEntryType(Invoice invoice) {
        if (OTHER.equals(invoice.getType()))
            return invoice.getBalance().signum() > 0 ? "D" : "C";

        return ENTRY_TYPES.get(invoice.getType());
    }

    private FileSystemResource getResource(Datastream datastream) throws IOException {
        String path = datastream.getOrgId() + "/bnp_balance/" + datastream.getPath();

        toExport.getFile().toPath().resolve(path).toFile().mkdirs();

        return new FileSystemResource(
                toExport.getFile().toPath().resolve(path + "/" + datastream.getName()));
    }
}
