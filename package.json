{"name": "afakto", "version": "1.0.0", "private": true, "description": "A factoring web application", "repository": "https://github.com/Afakto/afakto", "license": "UNLICENSED", "author": "afakto", "type": "module", "workspaces": ["src/main/webapp"], "scripts": {"app:start": "./bin/mvnw -ntp --batch-mode", "app:up": "docker compose -f src/main/docker/app.yml up --wait", "backend:build-cache": "./bin/mvnw dependency:go-offline -ntp", "backend:debug": "./bin/mvnw -Dspring-boot.run.jvmArguments=\"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000\"", "backend:doc:test": "./bin/mvnw -ntp javadoc:javadoc --batch-mode", "backend:info": "./bin/mvnw --version", "backend:nohttp:test": "./bin/mvnw -ntp checkstyle:check --batch-mode", "backend:start": "./bin/mvnw -Dskip.installnodenpm -Dskip.npm -ntp --batch-mode", "backend:dev": "./bin/mvnw spring-boot:run", "backend:unit:test": "./bin/mvnw -ntp -Dskip.installnodenpm -Dskip.npm verify --batch-mode -Dlogging.level.ROOT=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.afakto=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF", "build": "npm run webapp:prod --", "build-watch": "concurrently 'npm run webapp:build:dev -- --watch' npm:backend:start", "ci:backend:test": "npm run backend:info && npm run backend:doc:test && npm run backend:nohttp:test && npm run backend:unit:test -- -P$npm_package_config_default_environment", "ci:e2e:package": "npm run java:$npm_package_config_packaging:$npm_package_config_default_environment -- -Pe2e -Denforcer.skip=true", "ci:e2e:prepare": "npm run ci:e2e:prepare:docker", "ci:e2e:prepare:docker": "npm run services:up --if-present && docker ps -a", "preci:e2e:server:start": "npm run services:db:await --if-present && npm run services:others:await --if-present", "ci:e2e:server:start": "java -jar target/e2e.$npm_package_config_packaging --spring.profiles.active=e2e,$npm_package_config_default_environment -Dlogging.level.ROOT=OFF -Dlogging.level.org.zalando=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.afakto=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF --logging.level.org.springframework.web=ERROR", "ci:e2e:teardown": "npm run ci:e2e:teardown:docker --if-present", "ci:e2e:teardown:docker": "docker compose -f src/main/docker/services.yml down -v && docker ps -a", "clean-www": "rimraf target/classes/static/", "cleanup": "rimraf target/", "docker:db:down": "docker compose -f src/main/docker/postgresql.yml down -v", "docker:db:up": "docker compose -f src/main/docker/postgresql.yml up --wait", "docker:keycloak:down": "docker compose -f src/main/docker/keycloak.yml down -v", "docker:keycloak:up": "docker compose -f src/main/docker/keycloak.yml up --wait", "java:docker": "./bin/mvnw -ntp verify -DskipTests -Pprod jib:dockerBuild", "java:docker:arm64": "npm run java:docker -- -Djib-maven-plugin.architecture=arm64", "java:docker:dev": "npm run java:docker -- -Pdev,webapp", "java:docker:prod": "npm run java:docker -- -P<PERSON>rod", "java:jar": "./bin/mvnw -ntp verify -DskipTests --batch-mode", "java:jar:dev": "npm run java:jar -- -Pdev,webapp", "java:jar:prod": "npm run java:jar -- -P<PERSON>rod", "start": "concurrently -n \"back,front\" npm:app:start npm:webapp:dev", "test": "cd src/test/javascript && npx cypress open", "watch": "concurrently -n \"back,front\" \"npm run backend:dev\" \"npm run webapp:dev\"", "webapp:build": "npm run clean-www && npm run webapp:build:dev --", "webapp:build:dev": "npm run -w webapp build", "webapp:build:prod": "npm run -w webapp build", "webapp:dev": "npm run -w webapp dev", "webapp:prod": "npm run clean-www && npm run webapp:build:prod --", "webapp:test": "npm run -w webapp test --"}, "config": {"backend_port": 8080, "default_environment": "prod", "packaging": "jar"}, "devDependencies": {"concurrently": "9.1.2", "cypress": "14.3.2", "cypress-audit": "1.1.0", "generator-jhipster": "8.11.0", "rimraf": "^6.0.1"}}