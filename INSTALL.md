# AFAKTO

## Introduction

Afakto is a SaaS factoring solution that helps businesses optimize their cash
flow by selling their commercial receivables.

This project is built with JHipster and has been modified to use UUIDs instead
of sequential IDs. The frontend is developed using Quasar and Vue 3 for an
improved user experience.

🛠 Technologies Used

- Frontend: Vue 3, Quasar Framework
- Backend: Java, Spring Boot, JHipster
- Database: PostgreSQL

This guide provides all the necessary instructions to set up, develop, and run
the application.

This application was generated using JHipster, you can find documentation and
help at [https://www.jhipster.tech](https://www.jhipster.tech).

## Prerequisites

- Git
- OpenJDK 21+
- PostgreSQL
- NodeJS

## Project Structure

Node is required for generation and recommended for development. `package.json`
is always generated for a better development experience with prettier, commit
hooks, scripts and so on.

In the project root, JHipster generates configuration files for tools like git,
prettier, eslint, husk, and others that are well known and you can find
references in the web.

`/src/*` structure follows default Java structure.

- `.yo-rc.json` - Yeoman configuration file
  JH<PERSON>ster configuration is stored in this file at `generator-jhipster` key. You
  may find `generator-jhipster-*` for specific blueprints configuration.
- `.yo-resolve` (optional) - Yeoman conflict resolver
  Allows to use a specific action when conflicts are found skipping prompts for
  files that matches a pattern. Each line should match `[pattern] [action]` with
  pattern been a [Minimatch](https://github.com/isaacs/minimatch#minimatch)
  pattern and action been one of skip (default if omitted) or force. Lines
  starting with `#` are considered comments and are ignored.
- `.jhipster/*.json` - JHipster entity configuration files
- `./bin/npmw` - wrapper to use locally installed npm.
  JHipster installs Node and npm locally using the build tool by default. This
  wrapper makes sure npm is installed locally and uses it avoiding some
  differences different versions can cause. By using `./bin/npmw` instead of the
  traditional `npm` you can configure a Node-less environment to develop or test
  your application.
- `/src/main/webapp` - The Quasar Vue Front End

## Configuration

### Authentication with Auth0

A `.env` file should be shared with the team, to facilitate developments.
Following variable declaration is only there fore reference.

- Set environment variables
  replace xxx with Auth0 Application Settings
- For Insurers environment variable, please get in touch with the Lead Developper.

```sh
export OAUTH_URI=https://auth.afakto.com/
export OAUTH_CLIENT_ID=xxx (xxx from Client ID)
export OAUTH_CLIENT_SECRET=xxx (xxx from Client Secret)
export OAUTH_URI_MGMT=https://afakto.eu.auth0.com/
export OAUTH_CLIENT_ID_MGMT=xxx (xxx from Client ID of the management application)
export OAUTH_CLIENT_SECRET_MGMT=xxx (xxx from Client Secret of the management application)
export OAUTH_SCOPE="openid, profile, email, offline_access"

export INSEE_KEY=o5hN52rqpsePDF1EzkbgkkXshyEa
export INSEE_SECRET=dabIFK1aX1qzHu6C52DPi6sJT_sa

export ORG_MANTU_INSURER_ATRADIUS_APP_KEY=(from Atradius App Key)
export ORG_MANTU_INSURER_ATRADIUS_ID=(from Atradius ID)
export ORG_MANTU_INSURER_ATRADIUS_SECRET=(from Atradius secret)

export ORG_ANJAC_INSURER_COFACE_API_KEY=(from Coface API Key)
export ORG_ANJAC_INSURER_COFACE_LOGIN=(from Coface Login)
export ORG_ANJAC_INSURER_COFACE_PASSWORD=(from Coface Password)
```

### Backend

Before you can build this project, you must install and configure the following
dependencies on your machine.

### Directory

#### Linux

```sh
sudo mkdir /var/afakto
sudo chown 777 /var/afakto
```

#### Mac OS

```sh
sudo mkdir /var/afakto
sudo chmod 777 /var/afakto
```

### NodeJS

#### Linux

```sh
sudo apt install nodejs
```

#### Mac OS

```sh
brew install node
```

### PostgreSQL

#### Linux

```sh
sudo apt install postgresql
sudo -u postgres psql
```

```sql
ALTER USER postgres WITH PASSWORD 'afakto';
```

#### Mac OS

```sh
brew install postgresql
brew services start postgresql
psql postgres
```

If user "postgres" doesn't exist, run :

```sql
createuser -s postgres
psql postgres
ALTER USER postgres WITH PASSWORD 'afakto';
```

If the postgres user already exists, you can simply run :

```sql
psql postgres
ALTER USER postgres WITH PASSWORD 'afakto';
```

### DB check connection

Test connection using `psql`, `pgadmin` or <https://dbeaver.io>.

localhost:5432
postgres/afakto

### Frontend

The frontend code is located under quasar folder `/src/main/webapp`

[Node.js](https://nodejs.org/): We use Node to run a development web server and
build the project.

Depending on your system, you can install Node either from source or as a
pre-packaged bundle.

After installing Node, you should be able to run the following command to
install development tools.
You will only need to run this command when dependencies change in [package.json](https://www.notion.so/aksitreasury/package.json).

```sh
npm install
```

We provide a wrapper to launch npm.
You will only need to run this command when dependencies change in [package.json](https://www.notion.so/aksitreasury/package.json).

We use [npm](https://www.npmjs.com/) scripts and [Vite](https://vitejs.com/) as
our build system.

Run the following commands in two separate terminals to create a blissful
development experience where your browser auto-refreshes when files change on
your hard drive.

```sh
npm start
```

Npm is also used to manage CSS and JavaScript dependencies used in this
application. You can upgrade dependencies by specifying a newer version in
[package.json](https://www.notion.so/aksitreasury/package.json). You can also
run `npm update` and `npm install` to manage dependencies.
Add the `help` flag on any command to see how you can use it. For example,
`npm help update`.

The `npm run` command will list all the scripts available to run for this project.

### Managing dependencies

For example, to add Leaflet library as a runtime dependency of your application,
you would run following command:

```sh
npm install --save --save-exact leaflet
```

To benefit from TypeScript type definitions from DefinitelyTyped repository in
development, you would run following command:

```sh
npm install --save-dev --save-exact @types/leaflet
```

Then you would import the JS and CSS files specified in library's installation
instructions so that [Webpack](https://webpack.github.io/) knows about them:
Note: There are still a few other things remaining to do for Leaflet that we
won't detail here.

For further instructions on how to develop with JHipster, have a look at
[Using JHipster in development](https://www.jhipster.tech/documentation-archive/v8.8.0/development/).

## Building for production

### Packaging as jar

To build the final jar and optimize the AFAKTO application for production, run:

```sh
./bin/mvnw -Pprod clean verify
```

## Testing

### Spring Boot tests

To launch your application's tests, run:

```sh
./bin/mvnw verify
```

### [Not tested yet] Client tests

Unit tests are run by [Jest](https://facebook.github.io/jest/). They're located
in `src/test/javascript/` and can be run with:

```sh
npm test
```

UI end-to-end tests are powered by [Cypress](https://www.cypress.io/). They're
located in [src/test/javascript/cypress](https://www.notion.so/aksitreasury/src/test/javascript/cypress)
and can be run by starting Spring Boot in one terminal (`./bin/mvnw spring-boot:run`)
and running the tests (`npm run e2e`) in a second one.

### Cypress

Before running Cypress tests, specify Auth0 user credentials by overriding the
`CYPRESS_E2E_USERNAME` and `CYPRESS_E2E_PASSWORD` environment variables.

```sh
export CYPRESS_E2E_USERNAME="<your-username>"
export CYPRESS_E2E_PASSWORD="<your-password>"
```

See Cypress' documentation for setting OS [environment variables](https://docs.cypress.io/guides/guides/environment-variables#Setting)
to learn more.

**Auth0 requires a user to provide authorization consent on the first login.**
Consent flow is currently not handled in the Cypress test suite. To mitigate the
issue, you can use a user account that has already granted consent to authorize
application access via interactive login.

## Others

### Liquibase

During development, when the java domain changes:

- Generate a changeset with

```sh
./bin/mvnw liquibase:diff
```

The resulting change log file will be generated under src/main/resources/config/liquibase/changelog

- Modify it if necessary
- Add it
  In master.xml

```xml
<include file="config/liquibase/changelog/000000000000xxx_changelog.xml" relativeToChangelogFile="false"/>
```

### Code quality using Sonar

Sonar is used to analyse code quality. You can start a local Sonar server
(accessible on [http://localhost:9001](http://localhost:9001/)) with:

```sh
docker-compose -f src/main/docker/sonar.yml up -d
```

Note: we have turned off forced authentication redirect for UI in [src/main/docker/sonar.yml](https://www.notion.so/aksitreasury/src/main/docker/sonar.yml)
for out of the box experience while trying out SonarQube, for real use cases
turn it back on.

You can run a Sonar analysis with using the [sonar-scanner](https://docs.sonarqube.org/display/SCAN/Analyzing+with+SonarQube+Scanner)
or by using the maven plugin.

Then, run a Sonar analysis:

```sh
./bin/mvnw -Pprod clean verify sonar:sonar -Dsonar.login=admin -Dsonar.password=admin
```

If you need to re-run the Sonar phase, please be sure to specify at least the
`initialize` phase since Sonar properties are loaded from the
sonar-project.properties file.

```sh
./bin/mvnw initialize sonar:sonar -Dsonar.login=admin -Dsonar.password=admin
```

Additionally, Instead of passing `sonar.password` and `sonar.login` as CLI
arguments, these parameters can be configured from [sonar-project.properties](https://www.notion.so/aksitreasury/sonar-project.properties)
as shown below:

```yaml
sonar.login=admin
sonar.password=admin
```

For more information, refer to the [Code quality page](https://www.jhipster.tech/documentation-archive/v8.8.0/code-quality/).
